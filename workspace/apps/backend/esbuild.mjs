import { build } from "esbuild";
import esbuildPluginPino from "esbuild-plugin-pino";
import glob from "tiny-glob";

const plugins = await glob("src/plugins/**/*.ts");
const routes = await glob("src/routes/**/*.ts");

await build({
  bundle: true,
  conditions: ["production"],
  entryPoints: ["src/server.ts", ...plugins, ...routes],
  format: "esm",
  logLevel: "info",
  minify: true,
  outdir: "build",
  inject: ["cjs-shim.ts"],
  platform: "node",
  target: "es2022",
  plugins: [
    // @ts-expect-error -- Seems to be a bug in the plugin
    esbuildPluginPino({ transports: ["pino-pretty"] }),
  ],
  sourcemap: true,
});

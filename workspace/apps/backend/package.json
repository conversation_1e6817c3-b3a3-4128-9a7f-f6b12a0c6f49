{"name": "@constellatio/backend", "version": "0.0.1", "private": true, "prettier": "@constellatio/prettier-config", "type": "module", "scripts": {"backfill-subscription-metrics": "pnpm with-env tsx src/lib/stripe/backfill-subscription-metrics.ts", "build": "rm -rf build && node esbuild.mjs", "compile": "tsc", "clean": "git clean -xdf .cache .turbo dist node_modules", "clear": "git clean -xdf .cache .turbo dist build", "typegen": "tsc --emitDeclarationOnly", "dev": "FASTIFY_AUTOLOAD_TYPESCRIPT=1 pnpm with-env bun --conditions=development --watch run src/server.ts", "legacy-dev": "pnpm with-env tsx watch --conditions=development --clear-screen=false src/server.ts", "typecheck": "tsc --noEmit", "with-env": "dotenv-run -v -- ", "with-development-env": "dotenv-run -e development -v -- ", "with-production-env": "dotenv-run -e production -v -- ", "with-staging-env": "dotenv-run -e staging -v -- ", "prettierSeperator": "--------------------------------------- FORMATTING ---------------------------------------", "format": "prettier --check . ", "format:fix": "prettier --write . ", "eslintSeperator": "--------------------------------------- LINTING ------------------------------------------", "lint": "eslint src", "lint:fix": "eslint --fix src", "separatorTunnel": "---------------------- TUNNEL ----------------------", "ngrok-auth": "ngrok authtoken $NGROK_AUTHTOKEN", "tunnel": "ngrok start --config=ngrok.yml constellatio-web-app"}, "exports": {"./routers/*": {"types": "./dist/trpc/routers/*.d.ts", "development": "./src/trpc/routers/*.ts", "default": "./dist/trpc/routers/*.js"}, "./lib/*": {"types": "./dist/lib/*.d.ts", "development": "./src/lib/*.ts", "default": "./dist/lib/*.js"}, "./trpc": {"types": "./dist/trpc/root.d.ts", "development": "./src/trpc/root.ts", "default": "./dist/trpc/root.js"}, "./utils/*": {"types": "./dist/utils/*.d.ts", "development": "./src/utils/*.ts", "default": "./dist/utils/*.js"}}, "dependencies": {"@ai-sdk/google": "catalog:", "@caisy/sdk": "1.3.3", "@constellatio/cms": "workspace:*", "@constellatio/cookies": "workspace:*", "@constellatio/db": "workspace:*", "@constellatio/db-to-search": "workspace:*", "@constellatio/env": "workspace:*", "@constellatio/meilisearch": "workspace:*", "@constellatio/schemas": "workspace:*", "@constellatio/shared": "workspace:*", "@constellatio/supabase": "workspace:*", "@constellatio/tsconfig": "workspace:*", "@constellatio/utility-types": "workspace:*", "@constellatio/utils": "workspace:*", "@fastify/autoload": "6.2.0", "@fastify/cookie": "11.0.2", "@fastify/cors": "11.0.1", "@fastify/helmet": "13.0.1", "@fastify/multipart": "9.0.3", "@fastify/rate-limit": "10.2.2", "@fastify/sensible": "6.0.3", "@fastify/under-pressure": "9.0.3", "@google-cloud/storage": "catalog:", "@supabase/ssr": "catalog:", "@supabase/supabase-js": "catalog:", "@trpc/server": "catalog:", "ai": "catalog:", "axios": "catalog:", "close-with-grace": "2.2.0", "date-fns": "catalog:", "device-detector-js": "catalog:", "fastify": "5.2.2", "fastify-plugin": "^5.0.1", "fastify-raw-body": "5.0.0", "fastify-type-provider-zod": "4.0.2", "google-auth-library": "9.15.1", "ioredis": "5.6.0", "loops": "4.1.0", "meilisearch": "catalog:", "pino-pretty": "13.0.0", "postgres": "catalog:", "rate-limiter-flexible": "6.2.1", "slug": "10.0.0", "stripe": "catalog:", "superjson": "catalog:", "zod": "catalog:"}, "devDependencies": {"@constellatio/eslint-config": "workspace:*", "@constellatio/prettier-config": "workspace:*", "@constellatio/tsconfig": "workspace:*", "@total-typescript/ts-reset": "catalog:", "@types/node": "catalog:", "@types/slug": "5.0.9", "esbuild": "0.25.1", "esbuild-plugin-pino": "2.2.2", "eslint": "catalog:", "prettier": "catalog:", "tiny-glob": "0.2.9", "tsc-alias": "1.8.11", "tsx": "catalog:", "typescript": "catalog:"}}
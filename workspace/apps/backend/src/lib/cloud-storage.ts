import { env } from "@constellatio/env";
import { Storage } from "@google-cloud/storage";
import { type CredentialBody } from "google-auth-library";

const base64ServiceAccount = env.WEBAPP_GOOGLE_SERVICE_ACCOUNT_BASE64;
const serviceAccountBuffer = Buffer.from(base64ServiceAccount, "base64");
const cloudStorageCredentials = JSON.parse(serviceAccountBuffer.toString()) as CredentialBody;

export const cloudStorage = new Storage({
  credentials: cloudStorageCredentials,
  projectId: env.WEBAPP_GOOGLE_CLOUD_STORAGE_PROJECT_ID,
});

/* eslint-disable max-lines */

import { createSupabaseClient } from "~/lib/supabase.js";
import { finishSignup, type FinishSignUpProps } from "~/utils/signup.js";

import { cookies, safeParseCookieData } from "@constellatio/cookies";
import { eq, or, type SQL } from "@constellatio/db";
import { users } from "@constellatio/db/schema";
import { env } from "@constellatio/env";
import { idValidation } from "@constellatio/schemas/common/common.validation";
import { appPaths, authPaths } from "@constellatio/shared/paths";
import { queryParams } from "@constellatio/shared/query-params";
import { authProviders } from "@constellatio/shared/validation";
import { getDeviceInfo } from "@constellatio/utils/device-info";
import { splitFullName } from "@constellatio/utils/user";
import { type FastifyReply } from "fastify";
import { type FastifyPluginAsyncZod } from "fastify-type-provider-zod";
import { z } from "zod";

const redirectToErrorPage = async (reply: FastifyReply, error: unknown) =>
{
  let errorMessage: string;

  if (error instanceof Error)
  {
    errorMessage = error.message;
  }
  else if (error && typeof error === "object")
  {
    errorMessage = String(error);
  }
  else if (typeof error === "string")
  {
    errorMessage = error;
  }
  else
  {
    errorMessage = "An unknown error occurred";
  }

  await reply.redirect(
    `${env.NEXT_PUBLIC_SHARED_WEBAPP_URL}${authPaths.login}?${queryParams.socialAuthError}=${encodeURIComponent(errorMessage)}`
  );
};

const callbackProviderSchema = z.object({
  app_metadata: z.object({
    provider: z.enum(authProviders),
  }),
});

const appleCallbackSchema = z.object({
  email: z.string().email(),
  id: idValidation,
  provider: z.literal("apple"),
  user_metadata: z
    .object({
      full_name: z.string(),
      name: z.string(),
    })
    .partial(),
});

const googleCallbackSchema = z.object({
  email: z.string().email(),
  id: idValidation,
  provider: z.literal("google"),
  user_metadata: z
    .object({
      avatar_url: z.string(),
      full_name: z.string(),
      name: z.string(),
      picture: z.string(),
    })
    .partial(),
});

const linkedinCallbackSchema = z.object({
  email: z.string().email(),
  id: idValidation,
  provider: z.literal("linkedin_oidc"),
  user_metadata: z.object({
    family_name: z.string(),
    given_name: z.string(),
    picture: z.string().optional(),
  }),
});

const callbackSchema = z.discriminatedUnion("provider", [
  appleCallbackSchema,
  googleCallbackSchema,
  linkedinCallbackSchema,
]);

// eslint-disable-next-line @typescript-eslint/require-await
const plugin: FastifyPluginAsyncZod = async function (fastify, _opts)
{
  fastify.route({
    handler: async (request, reply) =>
    {
      const supabase = createSupabaseClient({ reply, request });
      const userAgent = request.headers["user-agent"];
      // const provider = request.query["auth-provider"];
      let userId: string;

      try
      {
        const { data, error } = await supabase.auth.exchangeCodeForSession(request.query.code);

        if (error)
        {
          console.error("Error while exchanging code for session: ", error);
          throw error;
        }

        userId = data.user.id;

        const existingUserQuery: SQL[] = [eq(users.id, data.user.id)];

        if (data.user.email)
        {
          existingUserQuery.push(eq(users.email, data.user.email));
        }

        const existingUser = await fastify.db.query.users.findFirst({
          where: or(...existingUserQuery),
        });

        if (existingUser)
        {
          const loginSearchParams = new URLSearchParams({
            [queryParams.socialLoginSuccess]: "true",
            /* ...(provider && {
              [queryParams.socialProvider]: provider,
            }),*/
          });

          return await reply.redirect(`${appPaths.dashboard}?${loginSearchParams.toString()}`);
        }

        const providerData = callbackProviderSchema.parse(data.user);

        const parsedCallbackData = callbackSchema.parse({
          ...data.user,
          provider: providerData.app_metadata.provider,
        });

        let additionalUserData: Pick<
          FinishSignUpProps["user"],
          "displayName" | "firstName" | "lastName" | "socialAuthProfilePictureUrl"
        >;

        switch (parsedCallbackData.provider)
        {
          case "apple":
          {
            const { firstName, lastName } = splitFullName(parsedCallbackData.user_metadata.full_name);

            additionalUserData = {
              displayName: parsedCallbackData.user_metadata.name || parsedCallbackData.email.split("@")[0] || null,
              firstName,
              lastName,
              socialAuthProfilePictureUrl: null,
            };
            break;
          }
          case "google":
          {
            const { firstName, lastName } = splitFullName(parsedCallbackData.user_metadata.full_name);

            additionalUserData = {
              displayName: parsedCallbackData.user_metadata.name || parsedCallbackData.email.split("@")[0] || null,
              firstName,
              lastName,
              socialAuthProfilePictureUrl:
                parsedCallbackData.user_metadata.avatar_url || parsedCallbackData.user_metadata.picture,
            };
            break;
          }
          case "linkedin_oidc":
          {
            additionalUserData = {
              displayName: `${parsedCallbackData.user_metadata.given_name} ${parsedCallbackData.user_metadata.family_name}`,
              firstName: parsedCallbackData.user_metadata.given_name,
              lastName: parsedCallbackData.user_metadata.family_name,
              socialAuthProfilePictureUrl: parsedCallbackData.user_metadata.picture,
            };
            break;
          }
        }

        const deviceInfo = getDeviceInfo(userAgent);

        const utmFirsTouchParsed = safeParseCookieData("utmFirst", request.cookies[cookies.utmFirst.name]);
        const utmLastTouchParsed = safeParseCookieData("utmLast", request.cookies[cookies.utmLast.name]);

        await finishSignup(fastify.db, {
          deviceInfo,
          supabaseServerClient: supabase,
          user: {
            ...additionalUserData,
            authProvider: parsedCallbackData.provider,
            email: parsedCallbackData.email,
            id: userId,
            utm: {
              firstTouch: utmFirsTouchParsed ?? null,
              lastTouch: utmLastTouchParsed ?? null,
            },
          },
        });
      }
      catch (e: unknown)
      {
        console.error("Error while finishing signup", e);
        await supabase.auth.signOut();
        return redirectToErrorPage(reply, e);
      }

      const signUpSearchParams = new URLSearchParams({
        [queryParams.socialSignUpSuccess]: "true",
        /* ...(provider && {
          [queryParams.socialProvider]: provider,
        }),*/
      });

      return reply.redirect(
        `${env.NEXT_PUBLIC_SHARED_WEBAPP_URL}${appPaths.dashboard}?${signUpSearchParams.toString()}`
      );
    },
    method: "GET",
    schema: {
      querystring: z.object({
        code: z.string(),
        // [queryParams.promoCode]: z.string().optional(),
        // [queryParams.authProvider]: z.string(),
      }),
      response: {
        200: z.object({
          email: z.string(),
          id: z.string(),
        }),
        400: z.object({
          error: z.any(),
          message: z.string(),
        }),
      },
    },
    url: "/callback",
  });
};

export default plugin;

/* eslint-disable max-lines */

import { createClickupTask } from "~/lib/clickup/tasks/create-task.js";
import { getClickupCrmUserByUserId, getUserFeedbackTaskCrmData } from "~/lib/clickup/utils.js";

import { eq } from "@constellatio/db";
import { users } from "@constellatio/db/schema";
import { env, isProduction } from "@constellatio/env";
import { formbricksWebhookSchema } from "@constellatio/schemas/integration/formbricks/feedback-received.schema";
import { type FastifyPluginAsyncZod } from "fastify-type-provider-zod";
import { z } from "zod";

// eslint-disable-next-line @typescript-eslint/require-await
const plugin: FastifyPluginAsyncZod = async function (fastify, _opts)
{
  fastify.route({
    handler: async (request, reply) =>
    {
      console.log(
        `----- ${new Date().toLocaleTimeString("de")} Formbricks Webhook received for received feedback -----`
      );

      const webhookRequestBody = request.body;

      if (!isProduction)
      {
        console.info("Automatic creation of clickup tasks is only enabled in production");
        return reply.send("Automatic creation of clickup tasks is only enabled in production");
      }

      if (webhookRequestBody.event === "testEndpoint")
      {
        console.log("Formbricks Webhook test endpoint reached.");
        return reply.send("Formbricks Webhook test endpoint reached.");
      }

      const user = await fastify.db.query.users.findFirst({
        where: eq(users.id, webhookRequestBody.data.person.userId),
      });

      const [userCrmTasks] = await getClickupCrmUserByUserId(user?.id);
      const clickupTaskData = getUserFeedbackTaskCrmData(webhookRequestBody.data, user, userCrmTasks?.id);
      await createClickupTask(env.WEBAPP_CLICKUP_FEEDBACK_LIST_ID, clickupTaskData);

      return reply.send("Success");
    },
    method: "POST",
    schema: {
      body: formbricksWebhookSchema,
      response: {
        200: z.string(),
      },
    },
    url: "/feedback-received",
  });
};

export default plugin;

import { addUserToCrmUpdateQueue } from "~/lib/clickup/utils.js";
import { handleCheckoutEvent } from "~/lib/stripe/handlers/checkouts/checkouts.handler.js";
import { handleInvoice } from "~/lib/stripe/handlers/invoices/invoice.handler.js";
import { handleSubscriptionEvent } from "~/lib/stripe/handlers/subscriptions/subscriptions.handler.js";
import { stripe } from "~/lib/stripe/stripe.js";

import { eq } from "@constellatio/db";
import { users } from "@constellatio/db/schema";
import { env } from "@constellatio/env";
import { type FastifyPluginAsyncZod } from "fastify-type-provider-zod";
import type Stripe from "stripe";
import { z } from "zod";

export const config = {
  api: {
    bodyParser: false,
  },
};

const crmRelevantEvents: Array<Stripe.Event["type"]> = [
  "customer.subscription.trial_will_end",
  "customer.subscription.resumed",
  "customer.subscription.created",
  "customer.subscription.paused",
  "customer.subscription.updated",
  "customer.subscription.deleted",
  "invoice.paid",
  "customer.updated",
  "payment_method.updated",
  "payment_method.attached",
  "payment_method.detached",
];

// eslint-disable-next-line @typescript-eslint/require-await
const plugin: FastifyPluginAsyncZod = async function (fastify, _opts)
{
  fastify.route({
    config: {
      rawBody: true,
    },
    handler: async (request, reply) =>
    {
      const event = await stripe.webhooks.constructEventAsync(
        request.rawBody!,
        request.headers["stripe-signature"],
        env.WEBAPP_STRIPE_SIGNING_SECRET
      );

      if (event.type.startsWith("customer.subscription"))
      {
        await handleSubscriptionEvent(fastify.db, event, event.data.object as Stripe.Subscription, fastify.redis);
      }
      else if (event.type.startsWith("invoice"))
      {
        await handleInvoice(fastify.db, event, event.data.object as Stripe.Invoice);
      }
      else if (event.type.startsWith("checkout"))
      {
        await handleCheckoutEvent(fastify.db, event, event.data.object as Stripe.Checkout.Session);
      }

      if (crmRelevantEvents.includes(event.type))
      {
        let customerId: string | undefined;

        if ("customer" in event.data.object)
        {
          const { customer } = event.data.object;
          customerId = typeof customer === "string" ? customer : customer?.id;
        }
        else if (event.data.object.object === "customer")
        {
          customerId = event.data.object.id;
        }

        if (customerId == null)
        {
          throw new Error("no customer id found in stripe event object");
        }

        const user = await fastify.db.query.users.findFirst({
          where: eq(users.stripeCustomerId, customerId),
        });

        await addUserToCrmUpdateQueue(fastify.db, user?.id);
      }

      return reply.send("Success");
    },
    method: "POST",
    schema: {
      headers: z.object({
        "stripe-signature": z.string(),
      }),
      response: {
        200: z.literal("Success"),
      },
    },
    url: "/",
  });
};

export default plugin;

import { type DbConnection } from "~/plugins/external/database.js";
import { addBadgeForUser } from "~/trpc/routers/badges/badges.services.js";
import { createStreakActivity } from "~/trpc/routers/streak/streak.services.js";

import { and, eq, gte, sum } from "@constellatio/db";
import {
  type ArticleReadSql,
  type CaseProgressSql,
  type FlashcardReviewLogSql,
  type FlashcardSql,
  type ForumAnswerSql,
  type ForumQuestionSql,
  pings,
  type PingSql,
  streak,
  type StreakSql,
} from "@constellatio/db/schema";
import { env } from "@constellatio/env";

export const streakHandlerPingInsert = async (db: DbConnection, record: PingSql["columns"]): Promise<void> =>
{
  const { UserId } = record;

  const latestStreakQuery = await db
    .select()
    .from(streak)
    .where(and(eq(streak.userId, UserId), eq(streak.lastSatisfiedDate, new Date())));

  if (latestStreakQuery.length > 0)
  {
    return;
  }

  const today = new Date();
  today.setHours(0, 0, 0, 0);

  const [result] = await db
    .select({
      totalPingInterval: sum(pings.pingInterval).mapWith(Number),
    })
    .from(pings)
    .where(and(eq(pings.userId, UserId), gte(pings.createdAt, today)));

  const totalPingInterval = result?.totalPingInterval ?? 0;

  if (totalPingInterval >= env.NEXT_PUBLIC_WEBAPP_STREAK_DAILY_TIME_ACTIVITY_THRESHOLD_SECONDS)
  {
    console.log(`Creating streak activity for user ${UserId} with activity type "time"`);
    await createStreakActivity(db, "time", UserId);
  }
};

export const streakHandlerForumQuestionInsert = async (
  db: DbConnection,
  record: ForumQuestionSql["columns"]
): Promise<void> =>
{
  const { UserId } = record;

  await createStreakActivity(db, "forumActivity", UserId);
};

export const streakHandlerForumAnswerInsert = async (
  db: DbConnection,
  record: ForumAnswerSql["columns"]
): Promise<void> =>
{
  const { UserId } = record;

  await createStreakActivity(db, "forumActivity", UserId);
};

export const streakHandlerCaseProgressInsert = async (
  db: DbConnection,
  record: CaseProgressSql["columns"]
): Promise<void> =>
{
  const { ProgressState, UserId } = record;
  if (ProgressState === "completed")
  {
    await createStreakActivity(db, "solvedCase", UserId);
  }
};

export const streakHandlerCaseProgressUpdate = async (
  db: DbConnection,
  record: CaseProgressSql["columns"]
): Promise<void> =>
{
  const { ProgressState, UserId } = record;
  if (ProgressState === "completed")
  {
    await createStreakActivity(db, "solvedCase", UserId);
  }
};

const handleBadges = async (db: DbConnection, satisfiedDays: number | null, userId: string) =>
{
  if (!satisfiedDays)
  {
    return;
  }

  if (satisfiedDays >= 5)
  {
    await addBadgeForUser(db, { badgeIdentifier: "streak-14", userId });
  }
  if (satisfiedDays >= 42)
  {
    await addBadgeForUser(db, { badgeIdentifier: "streak-42", userId });
  }
  if (satisfiedDays >= 84)
  {
    await addBadgeForUser(db, { badgeIdentifier: "streak-84", userId });
  }
};

export const streakHandlerStreakInsert = async (db: DbConnection, record: StreakSql["columns"]): Promise<void> =>
{
  const { SatisfiedDays, UserId } = record;
  await handleBadges(db, SatisfiedDays, UserId);
};

export const streakHandlerStreakUpdate = async (db: DbConnection, record: StreakSql["columns"]): Promise<void> =>
{
  const { SatisfiedDays, UserId } = record;
  await handleBadges(db, SatisfiedDays, UserId);
};

export const streakHandlerFlashcardInsertUpdateOrReviewLogInsert = async (
  db: DbConnection,
  record: FlashcardSql["columns"] | FlashcardReviewLogSql["columns"]
): Promise<void> =>
{
  const { UserId } = record;

  await createStreakActivity(db, "flashcard", UserId);
};

export const streakHandlerArticleReadInsertOrDelete = async (
  db: DbConnection,
  record: ArticleReadSql["columns"] | ArticleReadSql["columns"]
): Promise<void> =>
{
  const { UserId } = record;
  await createStreakActivity(db, "articleRead", UserId);
};

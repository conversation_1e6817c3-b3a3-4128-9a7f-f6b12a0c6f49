/* eslint-disable max-lines */

import { env } from "@constellatio/env";
import { type FastifyPluginAsyncZod } from "fastify-type-provider-zod";
import { z } from "zod";

import { bookmarkHandlerBookmarkInsert } from "./_handlers/bookmark.handler.js";
import { caseProgressHandler } from "./_handlers/caseProgress.handler.js";
import { forumActivityHandler } from "./_handlers/forumActivity.handler.js";
import { forumAnswerActivityHandlerCorrectAnswerInsert } from "./_handlers/forumAnswerActivity.handler.js";
import { gameProgressHandlerGameProgressInsert } from "./_handlers/gameProgress.handler.js";
import {
  streakHandlerArticleReadInsertOrDelete,
  streakHandlerCaseProgressInsert,
  streakHandlerCaseProgressUpdate,
  streakHandlerFlashcardInsertUpdateOrReviewLogInsert,
  streakHandlerForumAnswerInsert,
  streakHandlerForumQuestionInsert,
  streakHandlerPingInsert,
  streakHandlerStreakInsert,
  streakHandlerStreakUpdate,
} from "./_handlers/streak.handler.js";
import { ugcHandlerUploadedFileInsert } from "./_handlers/ugc.handler.js";
import { usageTimeHandlerPingInsert } from "./_handlers/usageTime.handler.js";
import { userHandlerUserInsert } from "./_handlers/user.handler.js";
import { type WebhookPayload } from "./_types.js";

// eslint-disable-next-line @typescript-eslint/require-await
const plugin: FastifyPluginAsyncZod = async function (fastify, _opts)
{
  fastify.route({
    handler: async (request, reply) =>
    {
      if (request.headers.authorization !== `Bearer ${env.WEBAPP_SUPABASE_WEBHOOK_SECRET}`)
      {
        console.error("Invalid supabase webhook secret");
        return reply.unauthorized();
      }

      const payload = request.body as WebhookPayload;
      console.log(`Supabase Webhook received on table '${payload.table}'`);

      switch (payload.table)
      {
        case "Ping":
          switch (payload.type)
          {
            case "INSERT":
              await streakHandlerPingInsert(fastify.db, payload.record);
              await usageTimeHandlerPingInsert(fastify.db, payload.record);
              break;
          }
          break;
        case "ForumAnswer":
          switch (payload.type)
          {
            case "INSERT":
              await streakHandlerForumAnswerInsert(fastify.db, payload.record);
              await forumActivityHandler(fastify.db, payload.record.UserId);
              break;
          }
          break;
        case "ForumQuestion":
          switch (payload.type)
          {
            case "INSERT":
              await streakHandlerForumQuestionInsert(fastify.db, payload.record);
              await forumActivityHandler(fastify.db, payload.record.UserId);
              break;
          }
          break;
        case "CorrectAnswer":
          switch (payload.type)
          {
            case "INSERT":
              await forumAnswerActivityHandlerCorrectAnswerInsert(fastify.db, payload.record);
              break;
          }
          break;
        case "CaseProgress":
          switch (payload.type)
          {
            case "INSERT":
              await streakHandlerCaseProgressInsert(fastify.db, payload.record);
              await caseProgressHandler(fastify.db, payload.record);
              break;
            case "UPDATE":
              await streakHandlerCaseProgressUpdate(fastify.db, payload.record);
              await caseProgressHandler(fastify.db, payload.record);
              break;
          }
          break;
        case "Streak":
          switch (payload.type)
          {
            case "INSERT":
              await streakHandlerStreakInsert(fastify.db, payload.record);
              break;
            case "UPDATE":
              await streakHandlerStreakUpdate(fastify.db, payload.record);
              break;
          }
          break;
        case "Bookmark":
        {
          switch (payload.type)
          {
            case "INSERT":
              await bookmarkHandlerBookmarkInsert(fastify.db, payload.record);
              break;
          }
          break;
        }
        case "GameProgress":
        {
          switch (payload.type)
          {
            case "INSERT":
            case "UPDATE":
              await gameProgressHandlerGameProgressInsert(fastify.db, payload.record);
              break;
          }
          break;
        }
        case "UploadedFile":
        {
          switch (payload.type)
          {
            case "INSERT":
              await ugcHandlerUploadedFileInsert(fastify.db, payload.record);
              break;
          }
          break;
        }
        case "User":
        {
          switch (payload.type)
          {
            case "INSERT":
              await userHandlerUserInsert(payload.record);
              break;
          }
          break;
        }
        case "ProfilePicture":
        {
          throw new Error("Case 'ProfilePicture' is not implemented yet");
        }
        case "Flashcard":
        {
          switch (payload.type)
          {
            case "INSERT":
            case "UPDATE":
              await streakHandlerFlashcardInsertUpdateOrReviewLogInsert(fastify.db, payload.record);
              break;
          }
          break;
        }
        case "FlashcardReviewLog":
        {
          switch (payload.type)
          {
            case "INSERT":
              await streakHandlerFlashcardInsertUpdateOrReviewLogInsert(fastify.db, payload.record);
              break;
          }
          break;
        }
        case "ArticleRead":
        {
          switch (payload.type)
          {
            case "INSERT":
              await streakHandlerArticleReadInsertOrDelete(fastify.db, payload.record);
              break;
            case "DELETE":
              await streakHandlerArticleReadInsertOrDelete(fastify.db, payload.old_record);
              break;
          }
          break;
        }
        case "UserOrder":
        {
          switch (payload.type)
          {
            case "INSERT":
            case "UPDATE":
              await fastify.redis.actions.deleteUserSubscriptionStatus(payload.record.UserId);
              break;
            case "DELETE":
              await fastify.redis.actions.deleteUserSubscriptionStatus(payload.old_record.UserId);
              break;
          }
          break;
        }
        default:
        {
          console.warn("Unknown table", payload);
        }
      }

      return reply.send("Success");
    },
    method: "POST",
    schema: {
      headers: z.object({
        authorization: z.string(),
      }),
      response: {
        200: z.string(),
      },
    },
    url: "/",
  });
};

export default plugin;

/* eslint-disable max-lines */
import { supabaseAdminClient } from "~/lib/supabase.js";

import { eq, or, type SQL } from "@constellatio/db";
import {
  answerUpvotes,
  bookmarks,
  casesProgress,
  casesSolutions,
  contentViews,
  documents,
  forumAnswers,
  forumQuestions,
  gamesProgress,
  notes,
  notifications,
  pings,
  profilePictures,
  questionUpvotes,
  referralBalances,
  referralCodes,
  referrals,
  streak,
  streakActivities,
  updateUserInCrmQueue,
  uploadedFiles,
  uploadFolders,
  users,
  usersToBadges,
  usersToRoles,
} from "@constellatio/db/schema";
import { env, isProduction } from "@constellatio/env";
import { addTrialForUserSchema } from "@constellatio/schemas/routers/admin/addTrialForUser.schema";
import { deleteUserSchema } from "@constellatio/schemas/routers/admin/deleteUser.schema";
import { makeUserConstellatioEmployeeSchema } from "@constellatio/schemas/routers/admin/makeUserConstellatioEmployee.schema";
import { makeUserFamilyMemberSchema } from "@constellatio/schemas/routers/admin/makeUserFamilyMember.schema";
import { printAllSettledPromisesSummary } from "@constellatio/utils/promise";

import { getUserByUserIdOrEmail } from "./admin.utils.js";
import { deleteClickupTask } from "../../../lib/clickup/tasks/delete-task.js";
import { findClickupTask } from "../../../lib/clickup/tasks/find-task.js";
import { type ClickupTask } from "../../../lib/clickup/types.js";
import { clickupCrmCustomField, getClickupCrmUserByUserId } from "../../../lib/clickup/utils.js";
import { deleteLoopsContactByEmail, deleteLoopsContactByUserId } from "../../../lib/loops/loops.js";
import { stripe } from "../../../lib/stripe/stripe.js";
import { BadRequestError, NotFoundError, SelfDeletionRequestError } from "../../../utils/serverError.js";
import { adminProcedure, createTRPCRouter } from "../../trpc.js";
import {
  addConstellatioEmployeeLifetimeSubscriptionToUserByUserID,
  addFriendsAndFamilyLifetimeSubscriptionToUserByUserID,
  addTrialToUserByUserID,
} from "../billing/trial.utils.js";

export const adminRouter = createTRPCRouter({
  addTrialToUser: adminProcedure
    .input(addTrialForUserSchema)
    .mutation(async ({ ctx: { db, redis }, input: { trialDays, type, userIdOrEmail } }) =>
    {
      const user = await getUserByUserIdOrEmail(db, userIdOrEmail, type);

      if (!user)
      {
        throw new NotFoundError();
      }

      const endDate = await addTrialToUserByUserID(db, redis, user.id, trialDays);

      return {
        endDate,
        trialDays,
        userEmail: user.email,
        userId: user.id,
      };
    }),
  deleteUser: adminProcedure
    .input(deleteUserSchema)
    .mutation(async ({ ctx: { adminUserId, db }, input: { type, userIdOrEmail } }) =>
    {
      const queryConditions: SQL[] = [];

      let userEmail: string | null = null;
      let userId: string | null = null;

      if (type === "id")
      {
        queryConditions.push(eq(users.id, userIdOrEmail));
        userId = userIdOrEmail;
      }
      else if (type === "email")
      {
        queryConditions.push(eq(users.email, userIdOrEmail));
        userEmail = userIdOrEmail;
      }
      else
      {
        throw new BadRequestError(new Error("Email or userId is required"));
      }

      const userToDelete = await db.query.users.findFirst({
        where: or(...queryConditions),
      });

      if (!userToDelete)
      {
        throw new NotFoundError();
      }

      if (userToDelete.id === adminUserId)
      {
        throw new SelfDeletionRequestError();
      }

      await db.transaction(async (transaction) =>
      {
        await transaction.delete(contentViews).where(eq(contentViews.userId, userToDelete.id));
        await transaction.delete(streakActivities).where(eq(streakActivities.userId, userToDelete.id));
        await transaction.delete(streak).where(eq(streak.userId, userToDelete.id));
        await transaction.delete(updateUserInCrmQueue).where(eq(updateUserInCrmQueue.userId, userToDelete.id));
        await transaction.delete(referralBalances).where(eq(referralBalances.userId, userToDelete.id));
        await transaction
          .delete(referrals)
          .where(or(eq(referrals.referringUserId, userToDelete.id), eq(referrals.referredUserId, userToDelete.id)));
        await transaction.delete(referralCodes).where(eq(referralCodes.userId, userToDelete.id));
        await transaction.delete(pings).where(eq(pings.userId, userToDelete.id));
        await transaction
          .delete(notifications)
          .where(or(eq(notifications.senderId, userToDelete.id), eq(notifications.recipientId, userToDelete.id)));
        await transaction.delete(usersToRoles).where(eq(usersToRoles.userId, userToDelete.id));
        await transaction.delete(answerUpvotes).where(eq(answerUpvotes.userId, userToDelete.id));
        await transaction.delete(questionUpvotes).where(eq(questionUpvotes.userId, userToDelete.id));
        await transaction.delete(forumAnswers).where(eq(forumAnswers.userId, userToDelete.id));
        await transaction.delete(forumQuestions).where(eq(forumQuestions.userId, userToDelete.id));
        await transaction.delete(usersToBadges).where(eq(usersToBadges.userId, userToDelete.id));
        await transaction.delete(gamesProgress).where(eq(gamesProgress.userId, userToDelete.id));
        await transaction.delete(casesSolutions).where(eq(casesSolutions.userId, userToDelete.id));
        await transaction.delete(casesProgress).where(eq(casesProgress.userId, userToDelete.id));
        await transaction.delete(notes).where(eq(notes.userId, userToDelete.id));
        await transaction.delete(documents).where(eq(documents.userId, userToDelete.id));
        await transaction.delete(uploadedFiles).where(eq(uploadedFiles.userId, userToDelete.id));
        await transaction.delete(uploadFolders).where(eq(uploadFolders.userId, userToDelete.id));
        await transaction.delete(bookmarks).where(eq(bookmarks.userId, userToDelete.id));
        await transaction.delete(profilePictures).where(eq(profilePictures.userId, userToDelete.id));
        await transaction.delete(users).where(eq(users.id, userToDelete.id));
        await supabaseAdminClient.auth.admin.deleteUser(userToDelete.id);

        if (userToDelete.stripeCustomerId)
        {
          await stripe.customers.del(userToDelete.stripeCustomerId);
        }
      });

      if (isProduction)
      {
        let clickupTasks: ClickupTask[];

        if (userEmail != null)
        {
          clickupTasks = await findClickupTask(env.WEBAPP_CLICKUP_CRM_LIST_ID, {
            custom_fields: [
              {
                field_id: clickupCrmCustomField.email.fieldId,
                operator: "=",
                value: userEmail,
              },
            ],
            include_closed: true,
          });
        }
        else
        {
          clickupTasks = await getClickupCrmUserByUserId(userId);
        }

        console.log(
          `found ${clickupTasks.length} clickup tasks for user ${userToDelete.email}`,
          clickupTasks.map((task) => task.id)
        );

        const results = await Promise.allSettled(clickupTasks.map(async (task) => deleteClickupTask(task.id)));

        printAllSettledPromisesSummary(results, "delete clickup tasks");
        if (userEmail != null)
        {
          await deleteLoopsContactByEmail(userEmail);
        }
        else if (userId)
        {
          await deleteLoopsContactByUserId(userId);
        }
        else
        {
          console.error(
            "Admin Delete User: Loops Contact could not be deleted because no email or userId was provided"
          );
        }
      }

      console.info(`Benutzer '${userToDelete.displayName}' mit E-Mail '${userToDelete.email}' erfolgreich gelöscht`);
    }),
  makeUserConstellatioEmployee: adminProcedure
    .input(makeUserConstellatioEmployeeSchema)
    .mutation(async ({ ctx: { db, redis }, input: { type, userIdOrEmail } }) =>
    {
      const user = await getUserByUserIdOrEmail(db, userIdOrEmail, type);

      if (!user)
      {
        throw new NotFoundError();
      }

      await addConstellatioEmployeeLifetimeSubscriptionToUserByUserID(db, redis, user.id);
    }),

  makeUserFamilyMember: adminProcedure
    .input(makeUserFamilyMemberSchema)
    .mutation(async ({ ctx: { db, redis }, input: { type, userIdOrEmail } }) =>
    {
      const user = await getUserByUserIdOrEmail(db, userIdOrEmail, type);

      if (!user)
      {
        throw new NotFoundError();
      }

      await addFriendsAndFamilyLifetimeSubscriptionToUserByUserID(db, redis, user.id);
    }),
});

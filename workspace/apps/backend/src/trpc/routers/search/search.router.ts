import { getDefaultSearchApiKey } from "~/lib/meilisearch.js";

import { getPopularSearches } from "@constellatio/cms/app/content/getPopularSearches";
import { insertedSearchQueries } from "@constellatio/db/schema";
import { type SearchIndex } from "@constellatio/db-to-search";
import { env } from "@constellatio/env";
import { generateTenantToken } from "meilisearch/token";
import { z } from "zod";

import { createTRPCRouter, protectedProcedure } from "../../trpc.js";

type SearchRules = {
  [key in SearchIndex]: {
    filter: string | undefined;
  };
};

export const searchRouter = createTRPCRouter({
  getAllCategories: protectedProcedure.query(async ({ ctx: { db } }) =>
  {
    const allLegalFields = await db.query.legalFields.findMany();
    const allSubFields = await db.query.subFields.findMany();
    const allTopics = await db.query.topics.findMany();
    return { allLegalFields, allSubFields, allTopics };
  }),

  getPopularSearch: protectedProcedure.query(async () =>
  {
    const popularSearchRes = await getPopularSearches();
    return popularSearchRes;
  }),

  getTenantToken: protectedProcedure.query(async ({ ctx: { userId } }) =>
  {
    const searchRules: SearchRules = {
      articles: {
        filter: undefined,
      },
      cases: {
        filter: undefined,
      },
      flashcards: {
        filter: `userId = ${userId}`,
      },
      "forum-questions": {
        filter: undefined,
      },
      tags: {
        filter: undefined,
      },
      "user-documents": {
        filter: `userId = ${userId}`,
      },
      "user-uploads": {
        filter: `userId = ${userId}`,
      },
    };

    const expiresAt = new Date(Date.now() + env.NEXT_PUBLIC_WEBAPP_MEILISEARCH_TENANT_TOKEN_EXPIRATION_TIME_MS);
    const defaultSearchApiKey = await getDefaultSearchApiKey();

    const token = await generateTenantToken({
      apiKey: defaultSearchApiKey.key,
      apiKeyUid: defaultSearchApiKey.uid,
      expiresAt,
      searchRules,
    });

    return token;
  }),

  logSearchQuery: protectedProcedure
    .input(
      z.object({
        query: z.string(),
        resultsCount: z.number(),
        tab: z.string(),
      })
    )
    .mutation(async ({ ctx: { db, userId }, input: { query, resultsCount, tab } }) =>
    {
      await db.insert(insertedSearchQueries).values({
        activeTab: tab,
        query,
        resultsCount,
        userId,
      });
    }),
});

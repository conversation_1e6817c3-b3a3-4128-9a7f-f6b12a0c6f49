import { type Bookmark } from "@constellatio/db/schema";

import { type UserWithRelations } from "../trpc/routers/users/users.service.js";

export type UserFiltered = Pick<
  UserWithRelations,
  | "email"
  | "id"
  | "gender"
  | "lastName"
  | "firstName"
  | "displayName"
  | "semester"
  | "university"
  | "roles"
  | "isForumModerator"
  | "isAdmin"
  | "profilePicture"
  | "authProvider"
  | "hasSeenLearningPathsExplanation"
>;

export const filterUserForClient = (user: UserWithRelations): UserFiltered => ({
  authProvider: user.authProvider,
  displayName: user.displayName,
  email: user.email,
  firstName: user.firstName,
  gender: user.gender,
  hasSeenLearningPathsExplanation: user.hasSeenLearningPathsExplanation,
  id: user.id,
  isAdmin: user.isAdmin,
  isForumModerator: user.isForumModerator,
  lastName: user.lastName,
  profilePicture: user.profilePicture,
  roles: user.roles,
  semester: user.semester,
  university: user.university,
});

export type BookmarkFiltered = Pick<Bookmark, "resourceId" | "resourceType">;

export const filterBookmarkForClient = (bookmark: Bookmark): BookmarkFiltered => ({
  resourceId: bookmark.resourceId,
  resourceType: bookmark.resourceType,
});

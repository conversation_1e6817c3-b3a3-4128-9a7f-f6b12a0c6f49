{"name": "@constellatio/web-app", "version": "0.0.1", "private": true, "type": "module", "main": "electron/main.js", "prettier": "@constellatio/prettier-config", "scripts": {"build": "pnpm with-env vite build", "check-formatting": "prettier --check .", "clean": "git clean -xdf .cache .turbo node_modules", "clear": "git clean -xdf .cache .turbo dist", "typegen": "tsc --noEmit", "dev": "pnpm with-env vite", "electron:build": "pnpm with-env vite build && electron-builder", "electron:dev": "pnpm with-env electron .", "serve": "pnpm with-env vite preview --port 3040", "typecheck": "tsc --noEmit", "generate-vercel-config": "tsx vercel/generate-vercel-config.ts", "ui-add": "pnpm dlx shadcn@latest add && prettier src --write --list-different", "with-env": "dotenv-run -v -- ", "prettierSeperator": "--------------------------------------- FORMATTING ---------------------------------------", "format": "prettier --check . ", "format:fix": "prettier --write . ", "eslintSeperator": "--------------------------------------- LINTING ------------------------------------------", "lint": "eslint src", "lint:fix": "eslint --fix src", "separatorGoogleCloud": "---------------------- GOOGLE CLOUD ----------------------", "copyServiceAccountBase64ToClipboard": "cat google/service-accounts/user-uploads-service-account.json | base64 | pbcopy && pnpm printCopiedToClipboard", "setGoogleCloudProject": "gcloud config set project idyllic-psyche-400317", "setCloudStorageLocalhostCors": "gsutil cors set google/cloud-storage-cors/localhost.cors-config.json gs://constellatio-user-uploads-local", "setPublicCloudStorageLocalhostCors": "gsutil cors set google/cloud-storage-cors/localhost.cors-config.json gs://constellatio-public-user-uploads-local", "setCloudStorageDevelopmentCors": "gsutil cors set google/cloud-storage-cors/development.cors-config.json gs://constellatio-user-uploads-development", "setPublicCloudStorageDevelopmentCors": "gsutil cors set google/cloud-storage-cors/development.cors-config.json gs://constellatio-public-user-uploads-development", "setCloudStorageStagingCors": "gsutil cors set google/cloud-storage-cors/staging.cors-config.json gs://constellatio-user-uploads-staging", "setPublicCloudStorageStagingCors": "gsutil cors set google/cloud-storage-cors/staging.cors-config.json gs://constellatio-public-user-uploads-staging", "setCloudStorageProductionCors": "gsutil cors set google/cloud-storage-cors/production.cors-config.json gs://constellatio-user-uploads-production", "setPublicCloudStorageProductionCors": "gsutil cors set google/cloud-storage-cors/production.cors-config.json gs://constellatio-public-user-uploads-production"}, "dependencies": {"@caisy/live-preview-javascript": "1.1.19", "@caisy/live-preview-react": "1.2.0", "@caisy/rich-text-react-renderer": "catalog:", "@caisy/ui-extension-react": "0.2.2", "@calcom/embed-react": "1.5.2", "@constellatio/backend": "workspace:*", "@constellatio/cms": "workspace:*", "@constellatio/db": "workspace:*", "@constellatio/db-to-search": "workspace:*", "@constellatio/env": "workspace:*", "@constellatio/fsrs": "workspace:*", "@constellatio/meilisearch": "workspace:*", "@constellatio/schemas": "workspace:*", "@constellatio/shared": "workspace:*", "@constellatio/shared-ui": "workspace:*", "@constellatio/supabase": "workspace:*", "@constellatio/utility-types": "workspace:*", "@constellatio/utils": "workspace:*", "@dnd-kit/core": "6.1.0", "@dnd-kit/sortable": "7.0.2", "@dnd-kit/utilities": "3.2.2", "@emotion/react": "11.11.4", "@emotion/server": "11.11.0", "@emotion/styled": "11.11.5", "@fontsource/karla": "5.2.5", "@fontsource/libre-baskerville": "5.2.5", "@formbricks/js": "4.1.0", "@hello-pangea/dnd": "16.6.0", "@hookform/resolvers": "catalog:", "@mantine/carousel": "6.0.21", "@mantine/core": "6.0.21", "@mantine/dates": "6.0.21", "@mantine/dropzone": "6.0.21", "@mantine/form": "6.0.21", "@mantine/hooks": "6.0.21", "@mantine/modals": "6.0.21", "@mantine/next": "6.0.21", "@mantine/notifications": "6.0.21", "@mantine/nprogress": "6.0.21", "@mantine/prism": "6.0.21", "@mantine/spotlight": "6.0.21", "@mantine/styles": "6.0.21", "@mantine/tiptap": "6.0.21", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.10", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.3", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-icons": "1.3.2", "@radix-ui/react-label": "2.1.0", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "1.1.1", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slot": "1.1.0", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.5", "@react-hook/resize-observer": "1.2.6", "@sentry/react": "9.17.0", "@sentry/vite-plugin": "3.4.0", "@supabase/auth-js": "2.64.3", "@supabase/gotrue-js": "2.70.0", "@supabase/ssr": "catalog:", "@supabase/supabase-js": "catalog:", "@tabler/icons-react": "3.3.0", "@tailwindcss/container-queries": "0.1.1", "@tailwindcss/vite": "4.0.14", "@tanstack/react-query": "catalog:", "@tanstack/react-query-devtools": "catalog:", "@tanstack/react-router": "1.114.29", "@tanstack/react-router-devtools": "1.114.29", "@tanstack/router-plugin": "1.114.29", "@tanstack/zod-adapter": "1.114.34", "@tiptap/core": "2.8.0", "@tiptap/extension-bullet-list": "2.11.5", "@tiptap/extension-code-block-lowlight": "2.10.3", "@tiptap/extension-color": "2.11.5", "@tiptap/extension-heading": "2.10.3", "@tiptap/extension-highlight": "2.8.0", "@tiptap/extension-horizontal-rule": "2.10.3", "@tiptap/extension-image": "2.10.3", "@tiptap/extension-link": "2.8.0", "@tiptap/extension-list-item": "2.11.5", "@tiptap/extension-ordered-list": "2.11.5", "@tiptap/extension-placeholder": "2.8.0", "@tiptap/extension-text-align": "2.8.0", "@tiptap/extension-text-style": "2.8.0", "@tiptap/extension-typography": "2.10.3", "@tiptap/extension-underline": "2.8.0", "@tiptap/pm": "2.8.0", "@tiptap/react": "2.8.0", "@tiptap/starter-kit": "2.8.0", "@trpc/client": "catalog:", "@trpc/react-query": "catalog:", "@trpc/server": "catalog:", "@trpc/tanstack-react-query": "catalog:", "axios": "catalog:", "browser-image-compression": "2.0.2", "canvas-confetti": "1.9.3", "class-variance-authority": "0.7.1", "clsx": "2.1.1", "cmdk": "1.0.0", "date-fns": "catalog:", "embla-carousel-react": "8.5.2", "fastest-levenshtein": "1.0.16", "file-saver": "2.0.5", "framer-motion": "12.0.1", "fuzzysort": "3.0.2", "i18next": "23.11.3", "immer": "10.1.1", "jotai": "2.8.0", "lowlight": "3.3.0", "lucide-react": "0.468.0", "meilisearch": "catalog:", "next-themes": "0.4.4", "nuqs": "2.4.1", "posthog-js": "catalog:", "raw-body": "2.5.2", "react": "catalog:", "react-calendly": "4.3.1", "react-day-picker": "8.10.1", "react-dom": "catalog:", "react-hook-form": "catalog:", "react-idle-timer": "5.7.2", "react-intersection-observer": "9.10.2", "react-medium-image-zoom": "5.2.12", "react-password-strength-bar": "0.4.1", "react-virtualized-auto-sizer": "1.0.24", "react-window": "1.8.10", "recharts": "2.15.2", "sonner": "1.7.1", "superjson": "catalog:", "tailwind-merge": "2.5.5", "tailwind-scrollbar": "catalog:", "tailwindcss": "catalog:", "tailwindcss-animate": "1.0.7", "ts-fsrs": "catalog:", "zod": "catalog:", "zod-i18n-map": "2.27.0", "zustand": "catalog:"}, "devDependencies": {"@constellatio/eslint-config": "workspace:*", "@constellatio/eslint-plugin-imagetools": "workspace:*", "@constellatio/prettier-config": "workspace:*", "@constellatio/tsconfig": "workspace:*", "@shadcn/ui": "0.0.4", "@tanstack/eslint-plugin-router": "1.114.12", "@types/canvas-confetti": "1.9.0", "@types/file-saver": "2.0.7", "@types/react": "catalog:", "@types/react-dom": "catalog:", "@vitejs/plugin-react": "4.3.4", "@vitejs/plugin-react-swc": "3.8.1", "autoprefixer": "10.4.20", "electron": "35.0.1", "electron-builder": "25.1.8", "electron-builder-squirrel-windows": "25.1.8", "eslint": "catalog:", "jiti": "catalog:", "postcss": "catalog:", "prettier": "catalog:", "prettier-plugin-merge": "0.7.2", "prettier-plugin-tailwindcss": "0.6.11", "typescript": "catalog:", "vite": "catalog:", "vite-imagetools": "7.0.5", "vite-plugin-electron": "0.29.0", "vite-plugin-environment": "1.1.3", "vite-plugin-radar": "0.10.0", "vite-plugin-svgr": "4.3.0", "vite-plugin-vercel": "9.0.5", "vite-tsconfig-paths": "5.1.4"}}
import { Link } from "@tanstack/react-router";
import { type LucideIcon } from "lucide-react";
import React, { type ComponentProps, type FunctionComponent, type ReactNode } from "react";

type Props = ComponentProps<typeof Link> & {
  readonly Icon: LucideIcon;
  readonly itemTitle: ReactNode | undefined;
};

export const NavLink: FunctionComponent<Props> = ({ Icon, itemTitle, ...props }) =>
{
  return (
    <Link
      {...props}
      activeOptions={{
        exact: false,
      }}
      className={"w-full flex flex-col items-center gap-1 py-1 hover:bg-muted-2 border-l-2 "}
      activeProps={{
        className: "text-sidebar-nav-option-active-foreground font-bold border-sidebar-nav-option-active-foreground",
      }}
      inactiveProps={{
        className: "text-sidebar-nav-option-inactive-foreground hover:text-sidebar-nav-option-hover border-white",
      }}>
      <Icon className="size-5" />
      {itemTitle && <span className="text-[12px] hyphens-auto tracking-tighter text-center">{itemTitle}</span>}
    </Link>
  );
};

/* eslint-disable max-lines */
import { NavLink } from "~/components/layout/sidebar/NavLink.tsx";
import { cn } from "~/lib/utils.ts";
import { useSidebarStore } from "~/stores/sidebar.store.ts";

import { appPaths } from "@constellatio/shared/paths";
import { FileText, GalleryVerticalEnd, Home, Library, type LucideIcon, Search, Waypoints } from "lucide-react";
import React, { useEffect } from "react";

import { ConstellatioLogo } from "../../ConstellatioLogo.tsx";
import { NavUser } from "../../user-menu-new.tsx";

interface NavItem
{
  icon: LucideIcon;
  path: string;
  title: string | React.ReactNode;
}

const navItems: NavItem[] = [
  {
    icon: Home,
    path: appPaths.dashboard,
    title: "Home",
  },
  {
    icon: Search,
    path: "/search",
    title: "Suche",
  },
  {
    icon: Library,
    path: appPaths.library,
    title: (
      <>
        Biblio-
        <br />
        thek
      </>
    ),
  },
  {
    icon: Waypoints,
    path: appPaths.learningPaths,
    title: (
      <>
        Lern-
        <br />
        pfade
      </>
    ),
  },
  {
    icon: GalleryVerticalEnd,
    path: appPaths.flashcards,
    title: (
      <>
        Kartei-
        <br />
        karten
      </>
    ),
  },
  {
    icon: FileText,
    path: appPaths.personalSpace,
    title: (
      <>
        Dein
        <br />
        Bereich
      </>
    ),
  },
];

export const sidebarPortalId = "sidebar-portal";
export const SIDEBAR_WRAPPER_ID = "sidebar-wrapper";
export const SIDEBAR_USER_MENU_ID = "sidebar-user-menu";

export function Sidebar()
{
  const hasPortalContent = useSidebarStore((state) => state.hasPortalContent);

  useEffect(() =>
  {
    useSidebarStore.getState().setIsSidebarMounted(true);
    return () => useSidebarStore.getState().setIsSidebarMounted(false);
  }, []);

  return (
    <div className={"flex flex-row h-svh sticky top-0 z-10"} id={SIDEBAR_WRAPPER_ID}>
      <div
        className={
          "bg-white h-full flex flex-col py-2 gap-5 px-0.5 items-center w-14 border-r border-muted-4 shadow-level1Sidebar"
        }>
        <ConstellatioLogo withTitle={false} svgProps={{ style: { height: 30, width: "auto" } }} />
        {navItems.map((item) => (
          <NavLink key={item.path} to={item.path} Icon={item.icon} itemTitle={item.title} />
        ))}
        <div className="mt-auto flex flex-col gap-5 w-full items-center">
          {/* TODO: auskommentiert da Forum einzige Benachrichtigung liefert und derzeit inaktiv ist */}
          {/* <NavLink to={appPaths.notifications} Icon={Bell} itemTitle={undefined} /> */}
          <div className="w-fit flex flex-col items-center gap-1">
            <NavUser />
          </div>
        </div>
      </div>
      {/* A seconds level sidebar can be injected with a portal */}
      <div id={sidebarPortalId} className={cn("h-svh relative", hasPortalContent && "ml-1")} />
    </div>
  );
}

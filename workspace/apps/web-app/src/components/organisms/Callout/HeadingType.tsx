import { BodyText } from "~/components/atoms/BodyText/BodyText.tsx";
import { AlertStoke } from "~/components/Icons/AlertStroke.tsx";
import { Article } from "~/components/Icons/Article.tsx";
import { Bookmark } from "~/components/Icons/Bookmark.tsx";
import { MedalIcon } from "~/components/Icons/MedalIcon.tsx";
import { NetworkChartIcon } from "~/components/Icons/NetworkChartIcon.tsx";
import { Pen } from "~/components/Icons/Pen.tsx";
import { Quote } from "~/components/Icons/Quote.tsx";
import { StarIcon } from "~/components/Icons/StarIcon.tsx";

import { type IGenCallout } from "@constellatio/cms/app/generated-types";
import { type FunctionComponent } from "react";

export const HeadingType: FunctionComponent<Pick<IGenCallout, "calloutType">> = ({ calloutType }) =>
{
  switch (calloutType)
  {
    case "remember":
      return (
        <>
          <StarIcon />
          <BodyText component="p" styleType="body-01-bold" tt="capitalize">
            Merke
          </BodyText>
        </>
      );

    case "bestPractice":
      return (
        <>
          <MedalIcon />
          <BodyText component="p" styleType="body-01-bold" tt="capitalize">
            Klausurtipp
          </BodyText>
        </>
      );

    case "definition":
      return (
        <>
          <Pen />
          <BodyText component="p" styleType="body-01-bold" tt="capitalize">
            Definition
          </BodyText>
        </>
      );

    case "quote":
      return (
        <>
          <Quote />
          <BodyText component="p" styleType="body-01-bold" tt="capitalize">
            Zitat
          </BodyText>
        </>
      );

    case "example":
      return (
        <>
          <Article />
          <BodyText component="p" styleType="body-01-bold" tt="capitalize">
            Beispiel
          </BodyText>
        </>
      );

    case "lawReference":
      return (
        <>
          <Bookmark />
          <BodyText component="p" styleType="body-01-bold" tt="capitalize">
            Gesetzesverweis
          </BodyText>
        </>
      );

    case "specialProblem":
      return (
        <>
          <AlertStoke />
          <BodyText component="p" styleType="body-01-bold" tt="capitalize">
            Problem
          </BodyText>
        </>
      );
    case "connectedLearning":
      return (
        <>
          <NetworkChartIcon />
          <BodyText component="p" styleType="body-01-bold" tt="capitalize">
            Vernetztes Lernen
          </BodyText>
        </>
      );

    default:
      return (
        <>
          <StarIcon />
          <BodyText component="p" styleType="body-01-bold" tt="capitalize">
            Merke
          </BodyText>
        </>
      );
  }
};

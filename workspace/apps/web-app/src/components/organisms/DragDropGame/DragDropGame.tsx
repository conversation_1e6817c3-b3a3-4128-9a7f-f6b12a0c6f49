/* eslint-disable max-lines */
import { BodyText } from "~/components/atoms/BodyText/BodyText.tsx";
import { Button } from "~/components/atoms/Button/Button.tsx";
import { Check } from "~/components/Icons/Check.tsx";
import { Gamification } from "~/components/Icons/Gamification.tsx";
import { Reload } from "~/components/Icons/Reload.tsx";
import { HelpNote } from "~/components/molecules/HelpNote/HelpNote.tsx";
import { ResultCard } from "~/components/molecules/ResultCard/ResultCard.tsx";
import { DragDropColumn } from "~/components/organisms/DragDropGame/DragDropColumn/DragDropColumn.tsx";
import useContextAndErrorIfNull from "~/hooks/useContextAndErrorIfNull.ts";
import { InvalidateQueriesContext } from "~/providers/InvalidateQueriesProvider.tsx";
import useDragDropGameStore, { type GameStatus, type TDragAndDropGameOptionType } from "~/stores/dragDropGame.store.ts";
import { api } from "~/utils/trpc.ts";

import { type IGenDragNDropGame } from "@constellatio/cms/app/generated-types";
import { shuffleArray } from "@constellatio/utils/array";
import { DragDropContext, type DropResult } from "@hello-pangea/dnd";
import { LoadingOverlay, Title } from "@mantine/core";
import { useMutation } from "@tanstack/react-query";
import { type FC, useCallback, useMemo } from "react";

import { Container, Game, GameWrapper, LegendWrapper, TitleWrapper } from "./DragDropGame.styles.ts";

export type TDragDropGame = Pick<IGenDragNDropGame, "game" | "helpNote" | "question"> & {
  readonly caseId: string;
  readonly id: string;
  readonly learningPathUnitTest?: {
    readonly currentLearningPathId: string;
    readonly currentLearningPathUnitTestId: string;
    readonly position: number;
    readonly totalGamesCount: number;
  };
};

export const DragDropGame: FC<TDragDropGame> = ({ caseId, game, helpNote, id, learningPathUnitTest, question }) =>
{
  const { mutate: insertLearningPathUnitCompletion } = useMutation(
    api.learningPath.insertLearningPathUnitCompletion.mutationOptions({
      onError: (error) => console.error("Error while inserting learning path unit completion", error),
    })
  );
  const { mutate: insertLearningPathUnitProgress } = useMutation(
    api.learningPath.insertLearningPathUnitProgress.mutationOptions({
      onError: (error) => console.error("Error while inserting learning path unit progress", error),
    })
  );
  const { invalidateGamesProgress } = useContextAndErrorIfNull(InvalidateQueriesContext);
  const { mutate: setGameProgress } = useMutation(
    api.gamesProgress.setGameProgress.mutationOptions({
      onError: (error) => console.error("Error while setting game progress", error),
      onSuccess: async () => invalidateGamesProgress({ caseId, queryType: "byCaseId" }),
    })
  );
  const { droppedItems, gameStatus, gameSubmitted, optionsItems, resultMessage } = useDragDropGameStore((s) =>
    s.getGameState({ caseId, gameData: game, gameId: id })
  );
  const updateGameState = useDragDropGameStore((s) => s.updateGameState);
  const gameOptions = game?.options;

  const originalOptions: TDragAndDropGameOptionType[] = useMemo(() =>
  {
    return gameOptions ?? [];
  }, [gameOptions]);

  const onDragEnd = useCallback(
    (result: DropResult) =>
    {
      if (!result.destination)
      {
        return;
      }
      const from = result.source.droppableId as "optionsItems" | "droppedItems";
      const to = result.destination.droppableId as "optionsItems" | "droppedItems";

      if (!from || !to)
      {
        return;
      }

      const { moveItem, reorderDroppedItems } = useDragDropGameStore.getState();

      if (from === "droppedItems" && to === "droppedItems")
      {
        reorderDroppedItems({
          gameId: id,
          itemSourceIndex: result.source.index,
          newPositionIndex: result.destination.index,
        });
      }
      else
      {
        moveItem({
          gameId: id,
          itemSourceIndex: result.source.index,
          newPositionIndex: result.destination.index,
          to,
        });
      }
    },
    [id]
  );

  const checkWinCondition = (): boolean =>
  {
    const areAllDroppedItemsCorrect = droppedItems.every((item) => item.correctAnswer);
    const areAllOptionsItemsIncorrect = optionsItems.every((item) => !item.correctAnswer);
    return areAllDroppedItemsCorrect && areAllOptionsItemsIncorrect;
  };

  const correctAnswersOrder = originalOptions
    .filter((item) => item.correctAnswer)
    .sort((a, b) => a.originalIndex - b.originalIndex);

  const checkOrder = (): boolean =>
  {
    for (let i = 0; i < droppedItems.length; i++)
    {
      if (droppedItems[i]?.id !== correctAnswersOrder[i]?.id)
      {
        return false;
      }
    }
    return true;
  };

  const onGameFinishHandler = (): void =>
  {
    const winCondition = checkWinCondition();
    let gameStatus: GameStatus;

    if (game?.orderRequired)
    {
      const orderCorrect = checkOrder();

      if (winCondition && orderCorrect)
      {
        gameStatus = "win";
        updateGameState({
          gameId: id,
          update: {
            gameStatus: "win",
            resultMessage: "Richtige Antwort",
          },
        });
      }
      else if (winCondition && !orderCorrect)
      {
        gameStatus = "lose-wrong-order";
        updateGameState({
          gameId: id,
          update: {
            gameStatus: "lose-wrong-order",
            resultMessage: "Richtige Antwort, falsche Reihenfolge",
          },
        });
      }
      else
      {
        gameStatus = "lose";
        updateGameState({
          gameId: id,
          update: {
            gameStatus: "lose",
            resultMessage: "Falsche Antwort",
          },
        });
      }
    }
    else
    {
      if (winCondition)
      {
        gameStatus = "win";
        updateGameState({
          gameId: id,
          update: {
            gameStatus: "win",
            resultMessage: "Richtige Antwort",
          },
        });
      }
      else
      {
        gameStatus = "lose";
        updateGameState({
          gameId: id,
          update: {
            gameStatus: "lose",
            resultMessage: "Falsche Antwort",
          },
        });
      }
    }

    if (!gameSubmitted)
    {
      // getNextGameIndex();
      updateGameState({
        gameId: id,
        update: { gameSubmitted: true },
      });
    }

    setGameProgress({
      gameId: id,
      gameResult: {
        correctAnswers: correctAnswersOrder,
        gameStatus,
        gameType: "DragDropGame",
        userAnswers: droppedItems,
      },
      progressState: "completed",
      wasSolvedCorrectly: gameStatus === "win",
    });

    if (learningPathUnitTest)
    {
      if (learningPathUnitTest.totalGamesCount === learningPathUnitTest.position + 1)
      {
        insertLearningPathUnitCompletion({
          learningPathId: learningPathUnitTest.currentLearningPathId,
          learningPathUnitTestId: learningPathUnitTest.currentLearningPathUnitTestId,
        });
      }
      else
      {
        insertLearningPathUnitProgress({
          learningPathId: learningPathUnitTest.currentLearningPathId,
          learningPathUnitTestId: learningPathUnitTest.currentLearningPathUnitTestId,
        });
      }
    }
  };

  const onGameResetHandler = (): void =>
  {
    const originalOptionsShuffled = shuffleArray<TDragAndDropGameOptionType>(originalOptions);

    updateGameState({
      gameId: id,
      update: {
        droppedItems: [],
        gameStatus: "inprogress",
        optionsItems: originalOptionsShuffled,
        resultMessage: "",
      },
    });
  };

  let renderedOptionsItems = optionsItems;

  if (renderedOptionsItems.length === 0 && droppedItems.length === 0)
  {
    renderedOptionsItems = originalOptions;
  }

  // To use live preview highlight the container
  // <Container {...getCaisyInspectProps({ fieldName: "DragNDropGame", id })}>

  return (
    <Container>
      <TitleWrapper>
        <Gamification />
        <Title order={4}>Drag & Drop</Title>
      </TitleWrapper>
      <GameWrapper>
        {question && (
          <BodyText component="p" styleType="body-01-regular">
            {question}
          </BodyText>
        )}
        {gameStatus !== "inprogress" && (
          <LegendWrapper>
            <BodyText component="p" styleType="body-01-regular">
              Richtig eingeordnet
            </BodyText>
            <BodyText component="p" styleType="body-01-regular">
              Falsch eingeordnet
            </BodyText>
          </LegendWrapper>
        )}
        <DragDropContext onDragEnd={onDragEnd}>
          <Game>
            <LoadingOverlay visible={optionsItems?.length === 0 && droppedItems?.length === 0} radius="radius-12" />
            <DragDropColumn
              gameId={id}
              gameStatus={gameStatus}
              columnType={"optionsItems"}
              isDraggingOverEnabled={false}
              isDropDisabled={false}
              items={renderedOptionsItems}
            />
            <DragDropColumn
              gameId={id}
              gameStatus={gameStatus}
              columnType={"droppedItems"}
              isDraggingOverEnabled={true}
              isDropDisabled={false}
              items={droppedItems}
            />
          </Game>
        </DragDropContext>
        {gameStatus !== "inprogress" && (
          <>
            <ResultCard
              hideCounter={true}
              droppedCorrectCards={droppedItems?.filter((item) => item.correctAnswer).length ?? null}
              totalCorrectCards={originalOptions.filter((item) => item.correctAnswer).length ?? null}
              variant={gameStatus === "win" ? "win" : "lose"}
              message={resultMessage ?? ""}
            />
            {helpNote?.json && <HelpNote data={helpNote} />}
          </>
        )}
        <div>
          <Button<"button">
            styleType="primary"
            size="large"
            leftIcon={gameStatus === "inprogress" ? <Check /> : <Reload />}
            onClick={() =>
            {
              if (gameStatus === "inprogress")
              {
                onGameFinishHandler();
              }
              else
              {
                onGameResetHandler();
              }
            }}
            disabled={gameStatus === "inprogress" && droppedItems.length < 1}>
            {gameStatus === "inprogress" ? "Antwort prüfen" : "Erneut lösen"}
          </Button>
        </div>
      </GameWrapper>
    </Container>
  );
};

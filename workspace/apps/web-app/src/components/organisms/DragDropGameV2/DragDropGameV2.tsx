/* eslint-disable max-lines */
import { BodyText } from "~/components/atoms/BodyText/BodyText.tsx";
import { Button } from "~/components/atoms/Button/Button.tsx";
import { Check } from "~/components/Icons/Check.tsx";
import { Gamification } from "~/components/Icons/Gamification.tsx";
import { Reload } from "~/components/Icons/Reload.tsx";
import { HelpNote } from "~/components/molecules/HelpNote/HelpNote.tsx";
import useContextAndErrorIfNull from "~/hooks/useContextAndErrorIfNull.ts";
import { InvalidateQueriesContext } from "~/providers/InvalidateQueriesProvider.tsx";
import useDragDropGameV2Store from "~/stores/dragDropGameV2.store.ts";
import { api } from "~/utils/trpc.ts";

import {
  type IGenDragNDropGameV2Category,
  type IGenDragNDropGameV2Option,
  type IGenDragNDropGameV2,
} from "@constellatio/cms/app/generated-types";
import { DragDropContext, type DropResult } from "@hello-pangea/dnd";
import { LoadingOverlay, Title } from "@mantine/core";
import { useMutation } from "@tanstack/react-query";
import { useCallback, type FC } from "react";

import { DragDropCategoryColumnV2 } from "./DragDropColumnsV2/DragDropCategoryColumnV2.tsx";
import { DragDropOptionsColumnV2 } from "./DragDropColumnsV2/DragDropOptionsColumnV2.tsx";
import {
  CategoriesWrapper,
  Container,
  Game,
  GameWrapper,
  LegendWrapper,
  TitleWrapper,
} from "./DragDropGameV2.styles.ts";
import { ResultCardV2 } from "./ResultCard/ResultCardV2.tsx";

export type TDragDropGameV2 = Pick<IGenDragNDropGameV2, "helpNote" | "question" | "categories" | "wrongOptions"> & {
  readonly caseId: string;
  readonly id: string;
  readonly learningPathUnitTest?: {
    readonly currentLearningPathId: string;
    readonly currentLearningPathUnitTestId: string;
    readonly position: number;
    readonly totalGamesCount: number;
  };
};

export const DragDropGameV2: FC<TDragDropGameV2> = ({
  caseId,
  categories,
  helpNote,
  id,
  learningPathUnitTest,
  question,
  wrongOptions,
}) =>
{
  const { filledCategories, gameStatus, optionsItems, originalOptionsItems, resultMessage } = useDragDropGameV2Store(
    (s) =>
      s.getGameState({
        caseId,
        gameData: {
          categories: (categories ?? []).filter(
            (category): category is IGenDragNDropGameV2Category => category != null
          ),
          wrongOptions: (wrongOptions ?? []).filter((option): option is IGenDragNDropGameV2Option => option != null),
        },
        gameId: id,
      })
  );
  const { mutate: insertLearningPathUnitCompletion } = useMutation(
    api.learningPath.insertLearningPathUnitCompletion.mutationOptions({
      onError: (error) => console.error("Error while inserting learning path unit completion", error),
    })
  );
  const { mutate: insertLearningPathUnitProgress } = useMutation(
    api.learningPath.insertLearningPathUnitProgress.mutationOptions({
      onError: (error) => console.error("Error while inserting learning path unit progress", error),
    })
  );
  const { invalidateGamesProgress } = useContextAndErrorIfNull(InvalidateQueriesContext);
  const { mutate: setGameProgress } = useMutation(
    api.gamesProgress.setGameProgress.mutationOptions({
      onError: (error) => console.error("Error while setting game progress", error),
      onSuccess: async () => invalidateGamesProgress({ caseId, queryType: "byCaseId" }),
    })
  );

  const { resetGameById, validateGameAnswers } = useDragDropGameV2Store.getState();

  const onDragEnd = useCallback(
    (result: DropResult) =>
    {
      if (!result.source)
      {
        console.error("no drag source");
        return;
      }
      if (!result.destination)
      {
        return;
      }

      const from = result.source.droppableId === "options" ? "options" : "category";
      const to = result.destination.droppableId === "options" ? "options" : "category";

      const { moveItem } = useDragDropGameV2Store.getState();
      moveItem({
        from,
        fromId: result.source.droppableId,
        gameId: id,
        optionId: result.draggableId,
        to,
        toId: result.destination.droppableId,
      });
    },
    [id]
  );

  const onGameFinishHandler = () =>
  {
    const gameWon = validateGameAnswers(id);

    if (gameWon === undefined)
    {
      console.error("Game won in drag drop game v2 is undefined");
      return;
    }

    setGameProgress({
      gameId: id,
      gameResult: {
        answers: {
          filledCategories: Object.values(filledCategories),
          undraggedOptions: Object.values(optionsItems),
        },
        correct: gameWon,
        gameType: "DragDropGameV2",
      },
      progressState: "completed",
      wasSolvedCorrectly: gameWon,
    });

    if (learningPathUnitTest)
    {
      if (learningPathUnitTest.totalGamesCount === learningPathUnitTest.position + 1)
      {
        insertLearningPathUnitCompletion({
          learningPathId: learningPathUnitTest.currentLearningPathId,
          learningPathUnitTestId: learningPathUnitTest.currentLearningPathUnitTestId,
        });
      }
      else
      {
        insertLearningPathUnitProgress({
          learningPathId: learningPathUnitTest.currentLearningPathId,
          learningPathUnitTestId: learningPathUnitTest.currentLearningPathUnitTestId,
        });
      }
    }
  };

  const onGameResetHandler = useCallback(() =>
  {
    resetGameById(id);
  }, [id, resetGameById]);

  return (
    <Container>
      <TitleWrapper>
        <Gamification />
        <Title order={4}>Drag & Drop (Kategorisierung)</Title>
      </TitleWrapper>
      <GameWrapper>
        {question && (
          <BodyText component="p" styleType="body-01-regular">
            {question}
          </BodyText>
        )}
        {gameStatus !== "inprogress" && (
          <LegendWrapper>
            <BodyText component="p" styleType="body-01-regular">
              Richtig eingeordnet
            </BodyText>
            <BodyText component="p" styleType="body-01-regular">
              Falsch eingeordnet
            </BodyText>
          </LegendWrapper>
        )}
        <DragDropContext onDragEnd={onDragEnd}>
          <Game>
            <LoadingOverlay
              visible={Object.values(optionsItems).length === 0 && Object.values(filledCategories).length === 0}
              radius="radius-12"
            />
            <DragDropOptionsColumnV2
              options={Object.values(optionsItems)}
              gameId={id}
              gameStatus={gameStatus}
              isDraggingOverEnabled={false}
              isDropDisabled={true}
            />
            <CategoriesWrapper>
              {Object.values(filledCategories).map((category) => (
                <DragDropCategoryColumnV2
                  key={category.category.id}
                  category={category}
                  gameId={id}
                  gameStatus={gameStatus}
                  isDraggingOverEnabled={false}
                  isDropDisabled={true}
                />
              ))}
            </CategoriesWrapper>
          </Game>
        </DragDropContext>
        {gameStatus !== "inprogress" && (
          <>
            <ResultCardV2 variant={gameStatus === "win" ? "win" : "lose"} message={resultMessage ?? ""} />
            {helpNote?.json && <HelpNote data={helpNote} />}
          </>
        )}
        <div>
          <Button<"button">
            styleType="primary"
            size="large"
            leftIcon={gameStatus === "inprogress" ? <Check /> : <Reload />}
            onClick={() =>
            {
              if (gameStatus === "inprogress")
              {
                onGameFinishHandler();
              }
              else
              {
                onGameResetHandler();
              }
            }}
            disabled={
              gameStatus === "inprogress" &&
              Object.values(optionsItems).length === Object.values(originalOptionsItems).length
            }>
            {gameStatus === "inprogress" ? "Antwort prüfen" : "Erneut lösen"}
          </Button>
        </div>
      </GameWrapper>
    </Container>
  );
};

import useOnboardingMeetingStatus from "~/hooks/useOnboardingMeetingStatus.ts";
import useSubscription from "~/hooks/useSubscription.ts";

import { differenceInDays } from "date-fns";

import TrialMeetingBlock from "./Blocks/TrialMeetingBlock.tsx";
import TrialNoMeetingBlock from "./Blocks/TrialNoMeetingBlock.tsx";
import { UnsubscribedHadTrialBlock } from "./Blocks/UnsubscribedHadTrial.tsx";
import UnsubscribedNoTrialBlock from "./Blocks/UnsubscribedNoTrialBlock.tsx";

export const UnsubscribedOrTrial = () =>
{
  const { data: subscriptionDetails, isLoading: isSubscriptionLoading } = useSubscription();
  const { data: onboardingMeetingStatus, isLoading: isOnboardingMeetingStatusLoading } = useOnboardingMeetingStatus();

  const getTrialEndText = (date: Date | null) =>
  {
    if (date === null) return "Resttage nicht bestimmbar";
    const days = differenceInDays(new Date(date), new Date());
    if (days === 0) return "Testzeitraum endet heute";
    if (days === 1) return "Testzeitraum endet in einem Tag";
    return `Testzeitraum endet in ${days} Tagen`;
  };

  if (isSubscriptionLoading || isOnboardingMeetingStatusLoading)
  {
    return null;
  }
  if (!subscriptionDetails)
  {
    console.error("OnboardingAndAbonement: Keine Subscription-Daten vorhanden (subscription ist null oder undefined)");
    return null;
  }
  if (subscriptionDetails.hasActiveSubscription)
  {
    return null;
  }

  console.log("subscriptionDetails", subscriptionDetails);

  if (subscriptionDetails.hasActiveTrial)
  {
    if (onboardingMeetingStatus?.hasMeeting)
    {
      return <TrialMeetingBlock trialEndText={getTrialEndText(subscriptionDetails.trialEnd)} />;
    }
    return <TrialNoMeetingBlock trialEndText={getTrialEndText(subscriptionDetails.trialEnd)} />;
  }

  if (subscriptionDetails.hasOrHadTrial)
  {
    return <UnsubscribedHadTrialBlock />;
  }

  return <UnsubscribedNoTrialBlock />;
};

/* eslint-disable max-lines */

import { StaticImage } from "~/components/atoms/StaticImage/StaticImage.tsx";
import StatusLabel from "~/components/atoms/statusLabel/StatusLabel.tsx";
import { Button } from "~/components/ui/button.tsx";
import { UnstyledButton } from "~/components/UnstyledButton.tsx";
import useContextAndErrorIfNull from "~/hooks/useContextAndErrorIfNull.ts";
import { useOnboardingTasksProgress } from "~/hooks/useOnboardingTasksProgress.ts";
import { supabase } from "~/lib/supabase.ts";
import { cn } from "~/lib/utils.ts";
import { InvalidateQueriesContext } from "~/providers/InvalidateQueriesProvider.tsx";

import { type OnboardingTaskId } from "@constellatio/shared/validation";
import { useNavigate } from "@tanstack/react-router";
import { type FunctionComponent, type ReactNode, useEffect, useState } from "react";

import ArticleImage from "./assets/articles.png?imagetools";
import CaseImage from "./assets/cases.png?imagetools";
import FlashcardImage from "./assets/flashcards.png?imagetools";
import LearningPathImage from "./assets/learningpaths.png?imagetools";

type OnboardingTask = {
  buttons: ReactNode;
  description: ReactNode;
  id: OnboardingTaskId;
  image: ImagetoolsOutput;
  title: string;
};

export const DashboardOnboardingTasksBlock: FunctionComponent = () =>
{
  const navigate = useNavigate();

  const onboardingTasks = [
    {
      buttons: [
        <Button variant="default" key="library" onClick={async () => navigate({ to: "/library" })}>
          Bibliothek
        </Button>,
        <Button variant="default" key="learning-paths" onClick={async () => navigate({ to: "/learning-paths" })}>
          Lernpfade
        </Button>,
        <Button key="search" variant="secondary" onClick={async () => navigate({ to: "/search" })}>
          Suche
        </Button>,
      ],
      description: (
        <div className={"flex flex-col gap-2 text-base text-muted-foreground"}>
          <h2 className={"font-bold text-lg pb-1"}>Lese deinen ersten Artikel und markiere ihn als gelesen</h2>
          <p>
            Constellatio bietet dir eine Vielzahl von Artikeln, die dir helfen, dein Wissen zu erweitern und zu
            vertiefen. Die Artikel sind in Zusammenarbeit mit Repetitoren entstanden und befinden sich auf
            Examensniveau. Unsere Artikel sind über verschiedene Wege auffindbar:
          </p>
          <ul className={"list-disc list-inside"}>
            <li>
              In der <b>Bibliothek</b> sind alle Artikel nach ihrem Rechtsgebiet geordnet.
            </li>
            <li>
              In den <b>Lernpfaden</b> sind die Artikel in den Lektionen mit Fällen gruppiert.
            </li>
            <li>
              In der <b>Suche</b> kannst du einfach nach einem Schlagwort suchen.
            </li>
          </ul>
          <p className={"text-sm text-muted-foreground"}>
            Hinweis: Für Artikel gibt es den gesehen und gelesen Status. Gesehen hast du den Artikel wenn du ihn einmal
            aufgemacht hast. Mit dem Status kannst du den Artikel mit Hilfe des roten &quot;Als gelesen markieren&quot;
            Button direkt über den Inhaltsverzeichnis versehen.
          </p>
        </div>
      ),
      id: "read-first-article",
      image: ArticleImage,
      title: "Ersten Artikel lesen",
    },
    {
      buttons: [
        <Button variant="default" key="library" onClick={async () => navigate({ to: "/library" })}>
          Bibliothek
        </Button>,
        <Button variant="default" key="learning-paths" onClick={async () => navigate({ to: "/learning-paths" })}>
          Lernpfade
        </Button>,
        <Button
          key="search"
          variant="secondary"
          onClick={async () =>
            navigate({ params: { caseId: "0fcdcecc-c9d4-4326-b12f-55488717c083" }, to: "/cases/$caseId" })
          }>
          Einführungsfall
        </Button>,
      ],
      description: (
        <div className={"flex flex-col gap-2 text-base text-muted-foreground"}>
          <h2 className={"font-bold text-lg pb-1"}>Löse deinen ersten Fall</h2>
          <p>
            Mit den interaktiven Fällen erarbeitest du dir dir selbstständig das Wissen für einen Sachverhalt. Das
            wissen was du dir angeeignet hast, wendest du sofort in der Praxis an und schreibst ein Gutachten. Usere
            Fälle sind über verschiedene Wege auffindbar:
          </p>
          <ul className={"list-disc list-inside"}>
            <li>
              In der <b>Bibliothek</b> sind alle Fälle nach ihrem Rechtsgebiet geordnet.
            </li>
            <li>
              In den <b>Lernpfaden</b> sind die Fälle in den Lektionen mit Fällen gruppiert.
            </li>
          </ul>
          <p>Zum kennenlernen der Fälle bearbeitest du am besten unseren Einführungsfall.</p>
        </div>
      ),
      id: "solve-first-case",
      image: CaseImage,
      title: "Ersten Fall lösen",
    },
    {
      buttons: [
        <Button variant="default" key="library" onClick={async () => navigate({ to: "/flashcards" })}>
          Karteikarten
        </Button>,
      ],
      description: (
        <div className={"flex flex-col gap-2 text-base text-muted-foreground"}>
          <h2 className={"font-bold text-lg pb-1"}>Erstelle deine erste Karteikarte</h2>
          <p>
            Mit den Constellatio Karteikarten kannst du dein Wissen in kleine Happen aufteilen. Mit unserem Spaced
            Repetition Algorithmus wiederholst du die Karteikarten genau dann wenn es notwendig ist. Wir bieten dir 4
            perfekt auf Juristen ausgerichtete Karteikartentypen an. Gereade der Schema- und der Problem-Karteikartentyp
            helfen dir dabei, dein Wissen perfekt zu verinnerlichen.
          </p>
          <p className={"text-sm text-muted-foreground"}>
            Hinweis: Jede Karteikarte muss einem Set zugewiesen sein. Entweder du erstellst zuerst ein Set oder du
            machst das während der Erstellung der Karteikarte.
          </p>
        </div>
      ),
      id: "create-first-flashcard",
      image: FlashcardImage,
      title: "Erste Karteikarte erstellen",
    },
    {
      buttons: [
        <Button variant="default" key="library" onClick={async () => navigate({ to: "/learning-paths" })}>
          Lernpfade
        </Button>,
      ],
      description: (
        <div className={"flex flex-col gap-2 text-base text-muted-foreground"}>
          <h2 className={"font-bold text-lg pb-1"}>Schließe deinen ersten Lernpfad test ab</h2>
          <p>
            Mit unseren Constellatio Lernpfaden leiten wir dich Schritt für Schritt durch die verschiedenen
            Rechtsgebiete. Die Lernpfade sind in Lektionen aufgeteilen. Zuerst erarbeitest du dir anhand von Fällen und
            Artikeln das Wissen. Am Ende jeder Lektion wirst du abgefragt um zu überprüfen, ob alles verinnerlicht hast.
          </p>
        </div>
      ),
      id: "complete-first-learning-path-test",
      image: LearningPathImage,
      title: "Ersten Lernpfad Test abschließen",
    },
  ] as const satisfies OnboardingTask[];

  const { data: onboardingTasksProgress } = useOnboardingTasksProgress();
  const { invalidateOnboardingTasksProgress } = useContextAndErrorIfNull(InvalidateQueriesContext);
  const [activeTask, setActiveTask] = useState<OnboardingTask>(onboardingTasks[0]);

  useEffect(() =>
  {
    const channel = supabase
      .channel("realtime onboarding tasks")
      .on("postgres_changes", { event: "INSERT", schema: "public", table: "OnboardingTaskProgress" }, (_payload) =>
      {
        void invalidateOnboardingTasksProgress();
      })
      .on("postgres_changes", { event: "UPDATE", schema: "public", table: "OnboardingTaskProgress" }, (_payload) =>
      {
        void invalidateOnboardingTasksProgress();
      })
      .subscribe();

    return () => void supabase.removeChannel(channel);
  }, [invalidateOnboardingTasksProgress]);

  return (
    <div className={"w-full"}>
      <h2 className={"mb-5 font-bold text-[28px] leading-10"}>Onboarding abschließen</h2>
      <div className={"bg-muted-0 p-5 rounded-xl flex items-stretch gap-5"}>
        <div className={"flex flex-col gap-3 w-[36%]"}>
          {onboardingTasks.map((task) =>
          {
            const isActiveTask = task.id === activeTask.id;
            const status = onboardingTasksProgress?.find(({ taskId }) => taskId === task.id)?.status ?? "not-started";

            return (
              <UnstyledButton
                key={task.id}
                onClick={() => setActiveTask(task)}
                className={cn(
                  "p-4 rounded-lg bg-muted-0 flex justify-between items-center gap-4 border border-muted-4",
                  status === "completed" && "bg-muted-1",
                  isActiveTask && "border-muted-9"
                )}>
                <strong className={"font-medium text-base"}>{task.title}</strong>
                <StatusLabel
                  progressState={
                    status === "completed" ? "completed" : status === "in-progress" ? "in-progress" : "not-started"
                  }
                  overwrites={{
                    completed: "Abgeschlossen",
                    inProgress: "In Bearbeitung",
                    upcoming: "Offen",
                  }}
                />
              </UnstyledButton>
            );
          })}
        </div>
        <div className={"flex-1 bg-muted-1 rounded-lg px-5 py-12 flex items-center justify-center"}>
          <div className={"w-full max-w-[600px] flex flex-col gap-5 items-start justify-center"}>
            {activeTask.image && (
              <StaticImage alt={`Vorschaubild`} priority={false} src={activeTask.image} className={"rounded-lg"} />
            )}
            {activeTask.description}
            <div className={"flex gap-3 items-center"}>{activeTask.buttons}</div>
          </div>
        </div>
      </div>
    </div>
  );
};

/* eslint-disable max-lines */

import ContentWrapper from "~/components/helpers/contentWrapper/ContentWrapper.tsx";
import { Button } from "~/components/ui/button.tsx";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "~/components/ui/dialog.tsx";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel } from "~/components/ui/form.tsx";
import { Input } from "~/components/ui/input.tsx";
import { toast } from "~/components/ui/sonner.tsx";
import { usePageTitleUpdate } from "~/hooks/usePageTitleUpdate.ts";
import { api } from "~/utils/trpc.ts";

import {
  addTrialForUserSchema,
  type AddTrialForUserSchema,
} from "@constellatio/schemas/routers/admin/addTrialForUser.schema";
import { deleteUserSchema, type DeleteUserSchema } from "@constellatio/schemas/routers/admin/deleteUser.schema";
import {
  makeUserConstellatioEmployeeSchema,
  type MakeUserConstellatioEmployeeSchema,
} from "@constellatio/schemas/routers/admin/makeUserConstellatioEmployee.schema";
import {
  makeUserFamilyMemberSchema,
  type MakeUserFamilyMemberSchema,
} from "@constellatio/schemas/routers/admin/makeUserFamilyMember.schema";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation } from "@tanstack/react-query";
import { type FunctionComponent, useState } from "react";
import { useForm } from "react-hook-form";

import * as styles from "./AdminPage.styles.ts";

export const AdminPage: FunctionComponent = () =>
{
  usePageTitleUpdate("Admin");
  const [isConfirmTrialDialogOpen, setIsConfirmTrialDialogOpen] = useState(false);
  const [trialValues, setTrialValues] = useState<AddTrialForUserSchema | null>(null);
  const [isConfirmDeleteDialogOpen, setIsConfirmDeleteDialogOpen] = useState(false);
  const [deleteValues, setDeleteValues] = useState<DeleteUserSchema | null>(null);
  const [isConfirmMakeConstellatioEmployeeDialogOpen, setIsConfirmMakeConstellatioEmployeeDialogOpen] = useState(false);
  const [makeConstellatioEmployeeValues, setMakeConstellatioEmployeeValues] =
    useState<MakeUserConstellatioEmployeeSchema | null>(null);
  const [isConfirmMakeFriendsAndFamilyDialogOpen, setIsConfirmMakeFriendsAndFamilyDialogOpen] = useState(false);
  const [makeFriendsAndFamilyValues, setMakeFriendsAndFamilyValues] = useState<MakeUserFamilyMemberSchema | null>(null);

  const form = useForm<DeleteUserSchema>({
    defaultValues: { type: "id", userIdOrEmail: "" },
    mode: "onChange",
    resolver: zodResolver(deleteUserSchema),
  });

  const { isPending, mutate: deleteUser } = useMutation(
    api.admin.deleteUser.mutationOptions({
      onError: (error) =>
      {
        if (error.data?.clientError.identifier === "not-found")
        {
          toast.error("Benutzer nicht gefunden", {
            description:
              "Leider konnte kein Benutzer mit dieser ID oder E-Mail-Adresse gefunden werden. Bitte versuche es erneut.",
          });
        }
        else if (error.data?.clientError.identifier === "self-deletion-request-forbidden")
        {
          toast.error("Account löschen nicht möglich", {
            description:
              "Du kannst deinen eigenen Account nicht löschen. Bitte versuche es mit einem anderen Benutzer.",
          });
        }
        else
        {
          toast.error("Fehler beim Löschen des Benutzers", {
            description: "Bitte versuche es erneut. Oder kontaktiere den Support.",
          });
        }
      },
      onSuccess: () =>
      {
        form.reset();
        setIsConfirmDeleteDialogOpen(false);
        toast.success("Benutzer erfolgreich gelöscht", {
          description: "Der Benutzer wurde erfolgreich gelöscht.",
        });
      },
    })
  );

  const handleSubmit = form.handleSubmit((values) =>
  {
    setDeleteValues(values);
    setIsConfirmDeleteDialogOpen(true);
  });

  const confirmDeleteUser = () =>
  {
    if (deleteValues)
    {
      deleteUser(deleteValues);
    }
  };

  const trialForm = useForm<AddTrialForUserSchema>({
    defaultValues: { trialDays: 5, type: "id", userIdOrEmail: "" },
    mode: "onChange",
    resolver: zodResolver(addTrialForUserSchema),
  });

  const { isPending: isTrialPending, mutate: addTrial } = useMutation(
    api.admin.addTrialToUser.mutationOptions({
      onError: (error) =>
      {
        if (error.data?.clientError.identifier === "not-found")
        {
          toast.error("Benutzer nicht gefunden", {
            description: "Leider konnte kein Benutzer mit dieser ID gefunden werden. Bitte versuche es erneut.",
          });
        }
        else
        {
          toast.error("Fehler beim Hinzufügen des Trials", {
            description:
              "Leider ist beim Hinzufügen des Trials ein Fehler aufgetreten. Bitte versuche es erneut. Oder kontaktiere den Support.",
          });
        }
      },
      onSuccess(data)
      {
        trialForm.reset();
        setIsConfirmTrialDialogOpen(false);
        toast.success(`Der Trial für ${data.userId} wurde erfolgreich ${data.trialDays} Tage hinzugefügt.
                    Der Trial endet am ${data.endDate}.`);
      },
    })
  );

  const handleTrialSubmit = trialForm.handleSubmit((values) =>
  {
    setTrialValues(values);
    setIsConfirmTrialDialogOpen(true);
  });

  const confirmAddTrial = () =>
  {
    if (trialValues)
    {
      addTrial(trialValues);
    }
  };

  const makeConstellatioEmployeeForm = useForm<MakeUserConstellatioEmployeeSchema>({
    defaultValues: { type: "id", userIdOrEmail: "" },
    mode: "onChange",
    resolver: zodResolver(makeUserConstellatioEmployeeSchema),
  });

  const { isPending: isMakeConstellatioEmployeePending, mutate: makeUserConstellatioEmployee } = useMutation(
    api.admin.makeUserConstellatioEmployee.mutationOptions({
      onError: (error) =>
      {
        toast.error("Fehler beim Hinzufügen des Constellatio Mitarbeiters", {
          description: "Bitte versuche es erneut. Oder kontaktiere den Support.",
        });
      },
      onSuccess: () =>
      {
        setIsConfirmMakeConstellatioEmployeeDialogOpen(false);
        toast.success("User zum Constellatio Mitarbeiter gemacht", {
          description: "Der User wurde erfolgreich zum Constellatio Mitarbeiter gemacht.",
        });
      },
    })
  );

  const handleMakeConstellatioEmployeeSubmit = makeConstellatioEmployeeForm.handleSubmit((values) =>
  {
    setMakeConstellatioEmployeeValues(values);
    setIsConfirmMakeConstellatioEmployeeDialogOpen(true);
  });

  const confirmMakeConstellatioEmployee = () =>
  {
    if (makeConstellatioEmployeeValues)
    {
      makeUserConstellatioEmployee(makeConstellatioEmployeeValues);
    }
  };

  const makeFriendsAndFamilyForm = useForm<MakeUserFamilyMemberSchema>({
    defaultValues: { type: "id", userIdOrEmail: "" },
    mode: "onChange",
    resolver: zodResolver(makeUserFamilyMemberSchema),
  });

  const { isPending: isMakeFriendsAndFamilyPending, mutate: makeUserFamilyMember } = useMutation(
    api.admin.makeUserFamilyMember.mutationOptions({
      onError: (error) =>
      {
        toast.error("Fehler beim Hinzufügen der Friends and Family", {
          description: "Bitte versuche es erneut. Oder kontaktiere den Support.",
        });
      },
      onSuccess: () =>
      {
        setIsConfirmMakeFriendsAndFamilyDialogOpen(false);
        toast.success("User zu Friends and Family gemacht", {
          description: "Der User wurde erfolgreich zu Friends and Family gemacht.",
        });
      },
    })
  );

  const handleMakeFriendsAndFamilySubmit = makeFriendsAndFamilyForm.handleSubmit((values) =>
  {
    setMakeFriendsAndFamilyValues(values);
    setIsConfirmMakeFriendsAndFamilyDialogOpen(true);
  });

  const confirmMakeFriendsAndFamily = () =>
  {
    if (makeFriendsAndFamilyValues)
    {
      makeUserFamilyMember(makeFriendsAndFamilyValues);
    }
  };
  return (
    <div css={styles.wrapper}>
      <ContentWrapper stylesOverrides={styles.contentWrapper}>
        <h1 className="text-4xl font-bold">Admin</h1>

        <Form {...trialForm}>
          <form onSubmit={handleTrialSubmit} css={styles.form}>
            <h2 className="text-2xl font-bold">Trial hinzufügen</h2>

            <FormField
              control={trialForm.control}
              name="userIdOrEmail"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel>E-Mail oder User ID</FormLabel>
                  <FormControl>
                    <Input placeholder="<EMAIL>" {...field} value={field.value || ""} />
                  </FormControl>
                  <FormDescription>
                    Die E-Mail Adresse oder ID des Users, der einen Trial erhalten soll.
                  </FormDescription>
                  {fieldState.error && (
                    <p className="text-sm font-medium text-destructive">{fieldState.error.message}</p>
                  )}
                </FormItem>
              )}
            />

            <FormField
              control={trialForm.control}
              name="trialDays"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel>Trial Tage</FormLabel>
                  <FormControl>
                    <Input type="number" placeholder="5" {...field} />
                  </FormControl>
                  <FormDescription>Die Anzahl der Tage, die der User einen Trial erhalten soll.</FormDescription>
                  {fieldState.error && (
                    <p className="text-sm font-medium text-destructive">{fieldState.error.message}</p>
                  )}
                </FormItem>
              )}
            />

            <Button type="submit">Trial hinzufügen</Button>
          </form>
        </Form>

        <Dialog open={isConfirmTrialDialogOpen} onOpenChange={setIsConfirmTrialDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Trial hinzufügen</DialogTitle>
            </DialogHeader>
            {trialValues && (
              <DialogDescription>
                Du möchtest der User <b>{trialValues.userIdOrEmail}</b> einen Trial von{" "}
                <b>{trialValues.trialDays} Tagen</b> hinzufügen?
              </DialogDescription>
            )}
            <DialogFooter>
              <Button variant="secondary" onClick={() => setIsConfirmTrialDialogOpen(false)}>
                Abbrechen
              </Button>
              <Button className="font-bold" onClick={confirmAddTrial} disabled={isTrialPending}>
                Ja, Trial hinzufügen
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        <Form {...form}>
          <form onSubmit={handleMakeConstellatioEmployeeSubmit} css={styles.form}>
            <h2 className="text-2xl font-bold">User zum Constellatio Mitarbeiter machen</h2>

            <p>
              <mark>Achtung</mark>: Hiermit bekommt der User eine Lifetime Subscription.
            </p>

            <FormField
              control={makeConstellatioEmployeeForm.control}
              name="userIdOrEmail"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel>E-Mail oder User ID</FormLabel>
                  <FormControl>
                    <Input placeholder="<EMAIL>" {...field} value={field.value || ""} />
                  </FormControl>
                  <FormDescription>
                    Die E-Mail Adresse oder ID des Users, der zum Constellatio Mitarbeiter gemacht werden soll.
                  </FormDescription>
                  {fieldState.error && (
                    <p className="text-sm font-medium text-destructive">{fieldState.error.message}</p>
                  )}
                </FormItem>
              )}
            />

            <Button type="submit" disabled={isPending}>
              User promoten
            </Button>
          </form>
        </Form>

        <Dialog
          open={isConfirmMakeConstellatioEmployeeDialogOpen}
          onOpenChange={setIsConfirmMakeConstellatioEmployeeDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>User zum Constellatio Mitarbeiter machen</DialogTitle>
            </DialogHeader>
            {makeConstellatioEmployeeValues && (
              <DialogDescription>
                Bist du sicher, dass du den User <b>{makeConstellatioEmployeeValues.userIdOrEmail}</b> zum Constellatio
                Mitarbeiter machen möchtest?
              </DialogDescription>
            )}
            <DialogFooter>
              <Button variant="secondary" onClick={() => setIsConfirmMakeConstellatioEmployeeDialogOpen(false)}>
                Abbrechen
              </Button>
              <Button
                className="font-bold"
                onClick={confirmMakeConstellatioEmployee}
                disabled={isMakeConstellatioEmployeePending}>
                Ja, passt
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        <Form {...form}>
          <form onSubmit={handleMakeFriendsAndFamilySubmit} css={styles.form}>
            <h2 className="text-2xl font-bold">User zu Friends and Family machen</h2>

            <p>
              <mark>Achtung</mark>: Hiermit bekommt der User eine Lifetime Subscription.
            </p>

            <FormField
              control={makeFriendsAndFamilyForm.control}
              name="userIdOrEmail"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel>E-Mail oder User ID</FormLabel>
                  <FormControl>
                    <Input placeholder="<EMAIL>" {...field} value={field.value || ""} />
                  </FormControl>
                  <FormDescription>
                    Die E-Mail Adresse oder ID des Users, der zu Friends and Family gemacht werden soll.
                  </FormDescription>
                  {fieldState.error && (
                    <p className="text-sm font-medium text-destructive">{fieldState.error.message}</p>
                  )}
                </FormItem>
              )}
            />

            <Button type="submit" disabled={isPending}>
              User promoten
            </Button>
          </form>
        </Form>

        <Dialog
          open={isConfirmMakeFriendsAndFamilyDialogOpen}
          onOpenChange={setIsConfirmMakeFriendsAndFamilyDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>User zu Friends and Family machen</DialogTitle>
            </DialogHeader>
            {makeFriendsAndFamilyValues && (
              <DialogDescription>
                Bist du sicher, dass du den User <b>{makeFriendsAndFamilyValues.userIdOrEmail}</b> zu Friends and Family
                machen möchtest?
              </DialogDescription>
            )}
            <DialogFooter>
              <Button variant="secondary" onClick={() => setIsConfirmMakeFriendsAndFamilyDialogOpen(false)}>
                Abbrechen
              </Button>
              <Button
                className="font-bold"
                onClick={confirmMakeFriendsAndFamily}
                disabled={isMakeFriendsAndFamilyPending}>
                Ja, passt
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        <Form {...form}>
          <form onSubmit={handleSubmit} css={styles.form}>
            <h2 className="text-2xl font-bold">User löschen</h2>

            <p>
              <mark>Achtung</mark>: Diese Funktion sollte nicht genutzt werden, um Accounts tatsächlicher Benutzer zu
              löschen. Diese Funktion sollte nur verwendet werden, um Accounts von Testbenutzern zu löschen. Neben den
              Einträgen in der Datenbank wird auch der Nutzer im Clickup CRM gelöscht.
            </p>

            <FormField
              control={form.control}
              name="userIdOrEmail"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel>E-Mail oder User ID</FormLabel>
                  <FormControl>
                    <Input placeholder="<EMAIL>" {...field} value={field.value || ""} />
                  </FormControl>
                  <FormDescription>Die E-Mail Adresse oder ID des Users, der gelöscht werden soll.</FormDescription>
                  {fieldState.error && (
                    <p className="text-sm font-medium text-destructive">{fieldState.error.message}</p>
                  )}
                </FormItem>
              )}
            />

            <Button type="submit" disabled={isPending}>
              User löschen
            </Button>
          </form>
        </Form>

        <Dialog open={isConfirmDeleteDialogOpen} onOpenChange={setIsConfirmDeleteDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>User löschen</DialogTitle>
            </DialogHeader>
            {deleteValues && (
              <DialogDescription>
                Bist du sicher, dass du den User <b>{deleteValues.userIdOrEmail}</b> löschen möchtest? Diese Aktion kann
                nicht rückgängig gemacht werden.
              </DialogDescription>
            )}
            <DialogFooter>
              <Button variant="secondary" onClick={() => setIsConfirmDeleteDialogOpen(false)}>
                Abbrechen
              </Button>
              <Button variant="destructive" className="font-bold" onClick={confirmDeleteUser} disabled={isPending}>
                Ja, User endgültig löschen
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </ContentWrapper>
    </div>
  );
};

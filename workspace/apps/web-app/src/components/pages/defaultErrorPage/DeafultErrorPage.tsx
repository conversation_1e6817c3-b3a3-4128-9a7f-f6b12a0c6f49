import { ConstellatioLogo } from "~/components/ConstellatioLogo.tsx";
import { Button } from "~/components/ui/button.tsx";
import { usePageTitleUpdate } from "~/hooks/usePageTitleUpdate.ts";

import { type FunctionComponent } from "react";

type Props = {
  readonly heading?: string;
  readonly message?: string;
};

export const DeafultErrorPage: FunctionComponent<Props> = ({ heading, message }) =>
{
  usePageTitleUpdate("Fehler");

  return (
    <div className={"w-full h-screen flex flex-col items-center justify-center gap-2 p-4"}>
      <ConstellatioLogo withTitle={true} svgProps={{ height: 22 }} />
      <h1 className={"mt-4"}>{heading || "Da ist leider etwas schief gelaufen"}</h1>
      <p className={"text-lg"}>{message || "Bitte versuche es später erneut oder wende dich an den Support."}</p>
      <Button className={"mt-3"} onClick={() => window.location.reload()}>
        Seite neu laden
      </Button>
    </div>
  );
};

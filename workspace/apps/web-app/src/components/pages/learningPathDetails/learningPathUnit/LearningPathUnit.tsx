import { Button } from "~/components/atoms/Button/Button.tsx";
import StatusLabel from "~/components/atoms/statusLabel/StatusLabel.tsx";
import { LearningPathUnitCompleted } from "~/components/Icons/LearningPathUnitCompleted.tsx";
import { LearningPathUnitInProgress } from "~/components/Icons/LearningPathUnitInProgress.tsx";
import { LearningPathUnitUpcoming } from "~/components/Icons/LearningPathUnitUpcoming.tsx";
import { LearningPathCompletedCard } from "~/components/pages/learningPathDetails/learningPathCompletedCard/LearningPathCompletedCard.tsx";
import { LearningPathContentPiece } from "~/components/pages/learningPathDetails/learningPathUnit/learningPathContentPiece/LearningPathContentPiece.tsx";
import { LearningPathTest } from "~/components/pages/learningPathDetails/learningPathUnit/learningPathTest/LearningPathTest.tsx";
import useContextAndErrorIfNull from "~/hooks/useContextAndErrorIfNull.ts";
import { type LearningPathWithProgress } from "~/hooks/useLearningPathProgress.ts";
import useSetArticleHasRead from "~/hooks/useSetArticleHasRead.ts";
import useSetCaseProgress from "~/hooks/useSetCaseProgress.ts";
import { InvalidateQueriesContext } from "~/providers/InvalidateQueriesProvider.tsx";

import { isProduction } from "@constellatio/env";
import { Title } from "@mantine/core";
import { type FunctionComponent, useState } from "react";

import * as sharedStyles from "../LearningPathDetails.styles.ts";
import * as styles from "./LearningPathUnit.styles.ts";

export type LearningPathUnitProps = {
  readonly index: number;
  readonly isLastUnit: boolean;
  readonly isLearningPathCompleted: boolean;
  readonly learningPathId: string;
  readonly unit: LearningPathWithProgress["units"][number];
};

export const LearningPathUnit: FunctionComponent<LearningPathUnitProps> = ({
  // index, TODO: check ob das raus kann, war schon drin aber weiß nicht wofür
  isLastUnit,
  isLearningPathCompleted,
  learningPathId,
  unit,
}) =>
{
  const [openedTest, setOpenedTest] = useState<string | null>(null);
  const {
    caseLearningTests,
    completedTasksCount,
    contentPieces,
    id,
    isCompleted,
    progressState,
    testProgressState,
    title,
    totalTasksCount,
  } = unit;

  const setCaseProgress = useSetCaseProgress();
  const setArticleHasRead = useSetArticleHasRead();
  const {
    invalidateAllLearningPathsProgress,
    invalidateCaseProgress,
    invalidateCasesProgress,
    invalidateLearningPaths,
    invalidateReadArticles,
  } = useContextAndErrorIfNull(InvalidateQueriesContext);

  const handleDevCompleteAll = async () =>
  {
    for (const piece of contentPieces)
    {
      if (piece.__typename === "Case")
      {
        await setCaseProgress.mutateAsync({ caseId: piece.id!, progressState: "completed" });
        await invalidateCaseProgress({ caseId: piece.id! });
      }
      else if (piece.__typename === "Article")
      {
        await setArticleHasRead.mutateAsync({ articleId: piece.id!, hasRead: true });
      }
    }

    await invalidateCasesProgress();
    await invalidateReadArticles();
    await invalidateLearningPaths();
    await invalidateAllLearningPathsProgress();
  };

  return (
    <div key={id} id={id!} css={styles.wrapper}>
      <div css={styles.visualPathWrapper}>
        <div css={progressState === "completed" && styles.iconWrapperCompleted}>
          {progressState === "completed" && <LearningPathUnitCompleted size={110} />}
          {progressState === "in-progress" && <LearningPathUnitInProgress size={110} />}
          {progressState === "not-started" && <LearningPathUnitInProgress size={110} />}
          {progressState === "upcoming" && <LearningPathUnitUpcoming size={110} />}
        </div>
        {!isLastUnit && <div css={styles.connectingLine(progressState === "completed")} />}
      </div>
      <div css={styles.unitWrapper}>
        <div
          css={[
            sharedStyles.card,
            styles.unit,
            progressState === "upcoming" && styles.unitDisabled,
            isCompleted && styles.unitCompleted,
          ]}>
          <div css={styles.unitTitleWrapper}>
            <Title order={2} css={styles.unitTitle}>
              {title}
            </Title>
            <div css={styles.unitProgressStateBadgeWrapper}>
              <StatusLabel progressState={progressState} overwrites={{ completed: "Abgeschlossen" }} />
            </div>
          </div>
          <p css={styles.unitCompletedCount}>
            {completedTasksCount}/{totalTasksCount}
          </p>
          <div css={styles.unitContentPieces}>
            {contentPieces
              ?.filter(Boolean)
              .map((contentPiece) => (
                <LearningPathContentPiece
                  key={contentPiece.id}
                  learningPathId={learningPathId}
                  unitId={unit.id!}
                  {...contentPiece}
                />
              ))}
          </div>
          {!isProduction && contentPieces?.length > 0 && (
            <Button
              styleType="secondarySimple"
              styleOverwrite={{ marginBottom: "1.4rem", whiteSpace: "normal", width: "100%", wordBreak: "break-word" }}
              size="medium"
              onClick={handleDevCompleteAll}>
              (DEV) Alle Fälle & Artikel als abgeschlossen markieren
            </Button>
          )}
          <ul css={styles.testList}>
            {caseLearningTests?.filter(Boolean).map((learningTest) =>
            {
              return (
                <LearningPathTest
                  key={learningTest.id}
                  learningPathId={learningPathId}
                  {...learningTest}
                  learningTest={learningTest}
                  openTest={() => setOpenedTest(learningTest.id!)}
                  openedTest={openedTest}
                  closeTest={() => setOpenedTest(null)}
                  testsCompleted={isCompleted ?? false}
                  testProgressState={testProgressState}
                  unitTitle={title ?? ""}
                />
              );
            })}
          </ul>
        </div>
        {isLearningPathCompleted && isLastUnit && <LearningPathCompletedCard />}
      </div>
    </div>
  );
};

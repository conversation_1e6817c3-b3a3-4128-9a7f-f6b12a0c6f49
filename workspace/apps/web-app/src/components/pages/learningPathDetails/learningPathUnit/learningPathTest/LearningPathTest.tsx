/* eslint-disable max-lines */
import { Button } from "~/components/atoms/Button/Button.tsx";
import StatusLabel from "~/components/atoms/statusLabel/StatusLabel.tsx";
import { Puzzle } from "~/components/Icons/Puzzle.tsx";
import { LearningPathTestDrawer } from "~/components/pages/learningPathDetails/learningPathUnit/learningPathTestDrawer/LearningPathTestDrawer.tsx";
import { type LearningPathUnitProps } from "~/components/pages/learningPathDetails/learningPathUnit/LearningPathUnit.tsx";
import useContextAndErrorIfNull from "~/hooks/useContextAndErrorIfNull.ts";
import useGamesProgress from "~/hooks/useGamesProgress.ts";
import type { LearningPathProgressState } from "~/hooks/useLearningPathProgress.ts";
import { cn } from "~/lib/utils.ts";
import { InvalidateQueriesContext } from "~/providers/InvalidateQueriesProvider.tsx";
import { api } from "~/utils/trpc.ts";

import { getGamesFromCase } from "@constellatio/cms/app/utils/case";
import { isProduction } from "@constellatio/env";
import { Title } from "@mantine/core";
import { useMutation } from "@tanstack/react-query";
import { useMemo, useState, type FunctionComponent } from "react";

type Props = {
  readonly closeTest: () => void;
  readonly learningPathId: string;
  readonly learningTest: LearningPathUnitProps["unit"]["caseLearningTests"][number];
  readonly openTest: (testId: string) => void;
  readonly openedTest: string | null;
  readonly testProgressState: LearningPathProgressState;
  readonly testsCompleted: boolean;
  readonly unitTitle: string;
};

export const LearningPathTest: FunctionComponent<Props> = ({
  closeTest,
  learningPathId,
  learningTest,
  openedTest,
  openTest,
  testProgressState,
  testsCompleted,
  unitTitle,
}) =>
{
  const { id } = learningTest;
  const {
    invalidateAllLearningPathsProgress,
    invalidateCaseProgress,
    invalidateCasesProgress,
    invalidateLearningPathProgress,
    invalidateLearningPaths,
  } = useContextAndErrorIfNull(InvalidateQueriesContext);

  const { isPending: isDevSkipPending, mutate: insertLearningPathUnitCompletion } = useMutation(
    api.learningPath.insertLearningPathUnitCompletion.mutationOptions({
      onSuccess: async (_data, variables) =>
      {
        await invalidateLearningPathProgress({ learningPathId });
        await invalidateLearningPaths();
        await invalidateAllLearningPathsProgress();
        if (variables?.learningPathUnitTestId)
        {
          await invalidateCaseProgress({ caseId: variables.learningPathUnitTestId });
        }
        await invalidateCasesProgress();
      },
    })
  );

  const [onlyShowIncorrectGames, setOnlyShowIncorrectGames] = useState(false);

  const title = "Testfragen " + unitTitle;

  let text: string;
  let buttonText: string | null;

  switch (testProgressState)
  {
    case "upcoming":
      text = "Schließe alle vorherigen Module ab, um diesen Test freizuschalten";
      buttonText = "Test starten";
      break;
    case "not-started":
      text = "Teste dein Wissen und schließe das Modul ab!";
      buttonText = "Test starten";
      break;
    case "in-progress":
      text = "Teste dein Wissen und schließe das Modul ab!";
      buttonText = "Test fortsetzen";
      break;
    case "completed":
      text = "Super! Du hast diesen Test erfolgreich abgeschlossen.";
      // buttonText = "Fortschritt zurücksetzen";
      buttonText = "Ganzen Test ansehen";
      break;
  }

  const games = useMemo(() => getGamesFromCase(learningTest), [learningTest]);

  const { data: gamesProgressForUnit } = useGamesProgress({
    caseId: learningTest.id!,
    queryType: "byCaseId",
  });

  const {
    areAllGamesCompleted,
    completedGamesCount,
    correctCompletedGamesCount,
    currentGameIndex,
    incorrectCompletedGameIds,
    incorrectCompletedGamesCount,
  } = useMemo(() =>
  {
    let currentGameIndex = 0;
    let correctCompletedGamesCount = 0;
    const incorrectCompletedGameIds: string[] = [];
    let incorrectCompletedGamesCount = 0;

    for (const game of games)
    {
      const gameProgress = gamesProgressForUnit?.find((gameProgress) => gameProgress.gameId === game.id);
      if (gameProgress && gameProgress.results.every(({ progressState }) => progressState === "not-started"))
      {
        continue;
      }

      if (currentGameIndex < games.length - 1)
      {
        currentGameIndex++;
      }

      const hasCompletedGame = gameProgress?.results.some(({ wasSolvedCorrectly }) => wasSolvedCorrectly);
      if (hasCompletedGame)
      {
        correctCompletedGamesCount++;
      }
      else
      {
        incorrectCompletedGamesCount++;
        incorrectCompletedGameIds.push(game.id as string);
      }
    }

    const completedGamesCount = correctCompletedGamesCount + incorrectCompletedGamesCount;
    const areAllGamesCompleted = completedGamesCount === games.length;

    return {
      areAllGamesCompleted,
      completedGamesCount,
      correctCompletedGamesCount,
      currentGameIndex,
      incorrectCompletedGameIds,
      incorrectCompletedGamesCount,
    };
  }, [games, gamesProgressForUnit]);

  return (
    <>
      <div>{learningTest.id}</div>
      <li>
        <div
          className={cn(
            "flex flex-wrap items-start justify-between gap-6 rounded-xl border p-4 transition-all duration-300 ease-in-out",
            {
              "border-cc-cases-3 bg-cc-cases-1": testProgressState === "in-progress",
              "border-muted-7 bg-muted-2": testProgressState === "not-started",
              "border-success-3 bg-white": testProgressState === "completed",
              "pointer-events-none border-muted-7 bg-muted-2 opacity-40": testProgressState === "upcoming",
            }
          )}>
          <div className="flex flex-row grow gap-4 items-center">
            <Puzzle size={32} />
            <div className="flex flex-1 flex-col items-start gap-2">
              <Title order={4}>{title}</Title>
              <p>{text}</p>
            </div>
          </div>
          <div />
          <div className="flex flex-col gap-4 grow">
            {testsCompleted && (
              <div className="flex flex-row gap-3 items-center justify-end">
                <div className="flex flex-row gap-1 text-gray-500 items-end">
                  <p className="font-bold text-xl">
                    {correctCompletedGamesCount}/{games.length}
                  </p>
                  <p className="text-base"> richtig</p>
                </div>
                <StatusLabel progressState={"completed"} overwrites={{ completed: "Abgeschlossen" }} />
              </div>
            )}
            <div className="flex flex-row gap-3 items-center justify-end">
              {testsCompleted && incorrectCompletedGamesCount > 0 && (
                <Button<"button">
                  onClick={() =>
                  {
                    setOnlyShowIncorrectGames(true);
                    openTest(id!);
                  }}
                  styleType="secondarySimple">
                  Falsche anzeigen ({incorrectCompletedGamesCount})
                </Button>
              )}
              {buttonText && (
                <Button<"button">
                  onClick={() =>
                  {
                    openTest(id!);
                  }}
                  styleType="secondarySimple">
                  {buttonText}
                </Button>
              )}
            </div>
          </div>
        </div>
      </li>
      {/* DEV-Button: Test überspringen */}
      {!isProduction && (
        <Button<"button">
          styleType="secondarySimple"
          styleOverwrite={{ marginTop: "1.4rem", whiteSpace: "normal", width: "100%" }}
          size="medium"
          loading={isDevSkipPending}
          onClick={() =>
            insertLearningPathUnitCompletion({
              learningPathId,
              learningPathUnitTestId: id!,
            })
          }>
          (DEV) Test überspringen
        </Button>
      )}
      <LearningPathTestDrawer
        key={id}
        learningPathId={learningPathId}
        closeDrawer={async () =>
        {
          await invalidateLearningPathProgress({ learningPathId });
          await invalidateLearningPaths();
          await invalidateAllLearningPathsProgress();
          await invalidateCaseProgress({ caseId: id! });
          await invalidateCasesProgress();
          void closeTest();
          setOnlyShowIncorrectGames(false);
        }}
        caseLearningTest={learningTest}
        caseLearningTestId={learningTest.id!}
        isOpened={openedTest === learningTest.id}
        games={games}
        incorrectCompletedGameIds={incorrectCompletedGameIds}
        completedGamesCount={completedGamesCount}
        areAllGamesCompleted={areAllGamesCompleted}
        currentGameIndex={currentGameIndex}
        onlyShowIncorrectGames={onlyShowIncorrectGames}
        title={title}
      />
    </>
  );
};

/* eslint-disable max-lines */
import { Button } from "~/components/atoms/Button/Button.tsx";
import { richTextParagraphOverwrite } from "~/components/helpers/richTextParagraphOverwrite.tsx";
import { Richtext } from "~/components/molecules/Richtext/Richtext.tsx";
import SlidingPanelTitle from "~/components/molecules/slidingPanelTitle/SlidingPanelTitle.tsx";
import { DragDropGame } from "~/components/organisms/DragDropGame/DragDropGame.tsx";
import { DragDropGameV2 } from "~/components/organisms/DragDropGameV2/DragDropGameV2.tsx";
import FillGapsGame from "~/components/organisms/FillGapsGame/FillGapsGame.tsx";
import SelectionCardGame from "~/components/organisms/SelectionCardGame/SelectionCardGame.tsx";

import { type IGenCase } from "@constellatio/cms/app/generated-types";
import { type Games } from "@constellatio/cms/app/utils/case";
import { type IDocumentLink } from "@constellatio/shared-ui/richtext";
import { type Nullable } from "@constellatio/utility-types";
import { Drawer, ScrollArea, Title } from "@mantine/core";
import { type FunctionComponent, useCallback, useRef } from "react";

import * as styles from "./LearningPathTestDrawer.styles.ts";

type Props = {
  readonly areAllGamesCompleted: boolean;
  readonly caseLearningTest: Nullable<IGenCase>;
  readonly caseLearningTestId: string;
  readonly closeDrawer: () => void;
  readonly completedGamesCount: number;
  readonly currentGameIndex: number;
  readonly games: Games;
  readonly incorrectCompletedGameIds: string[];
  readonly isOpened: boolean;
  readonly learningPathId: string;
  readonly onlyShowIncorrectGames: boolean;
  readonly title: string;
};

export const LearningPathTestDrawer: FunctionComponent<Props> = ({
  areAllGamesCompleted,
  caseLearningTest,
  caseLearningTestId,
  closeDrawer,
  completedGamesCount,
  currentGameIndex,
  games,
  incorrectCompletedGameIds,
  isOpened,
  learningPathId,
  onlyShowIncorrectGames,
  title,
}) =>
{
  const content = caseLearningTest?.fullTextTasks;
  const onlyShowIncorrectGamesRef = useRef(onlyShowIncorrectGames);
  const incorrectCompletedGameIdsRef = useRef(incorrectCompletedGameIds);

  onlyShowIncorrectGamesRef.current = onlyShowIncorrectGames;
  incorrectCompletedGameIdsRef.current = incorrectCompletedGameIds;

  const documentLinkOverwrite = useCallback(
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    (props: any) =>
    {
      const node = props.node as unknown as IDocumentLink;

      return node && content?.connections
        ? content.connections?.map((component, index) =>
          {
            if (
              onlyShowIncorrectGamesRef.current &&
              component?.id &&
              !incorrectCompletedGameIdsRef.current.includes(component.id)
            )
              {
              return null;
            }

            switch (component?.__typename)
              {
              case "CardSelectionGame":
                if (component.id != null && node?.attrs?.documentId === component?.id)
                  {
                  return (
                    <div css={styles.gameWrapper}>
                      <SelectionCardGame
                        {...component}
                        caseId={caseLearningTestId}
                        id={component.id}
                        learningPathUnitTest={{
                          currentLearningPathId: learningPathId,
                          currentLearningPathUnitTestId: caseLearningTestId,
                          position: index,
                          totalGamesCount: games.length,
                        }}
                      />
                    </div>
                  );
                }
                break;
              case "DragNDropGame":
                if (component.id != null && node?.attrs?.documentId === component?.id)
                  {
                  return (
                    <div css={styles.gameWrapper}>
                      <DragDropGame
                        {...component}
                        caseId={caseLearningTestId}
                        id={component.id}
                        learningPathUnitTest={{
                          currentLearningPathId: learningPathId,
                          currentLearningPathUnitTestId: caseLearningTestId,
                          position: index,
                          totalGamesCount: games.length,
                        }}
                      />
                    </div>
                  );
                }
                break;
              case "DragNDropGameV2":
                if (component.id != null && node?.attrs?.documentId === component?.id)
                  {
                  return (
                    <div css={styles.gameWrapper}>
                      <DragDropGameV2
                        {...component}
                        caseId={caseLearningTestId}
                        id={component.id}
                        learningPathUnitTest={{
                          currentLearningPathId: learningPathId,
                          currentLearningPathUnitTestId: caseLearningTestId,
                          position: index,
                          totalGamesCount: games.length,
                        }}
                      />
                    </div>
                  );
                }
                break;
              case "FillInGapsGame":
                if (node?.attrs?.documentId === component?.id)
                  {
                  return (
                    <div css={styles.gameWrapper}>
                      <FillGapsGame
                        {...component}
                        caseId={caseLearningTestId}
                        learningPathUnitTest={{
                          currentLearningPathId: learningPathId,
                          currentLearningPathUnitTestId: caseLearningTestId,
                          position: index,
                          totalGamesCount: games.length,
                        }}
                      />
                    </div>
                  );
                }
                break;
              default:
                console.warn(`Unknown component type: ${component?.__typename}`);
                return null;
            }
            return null;
          })
        : null;
    },
    [caseLearningTestId, content?.connections, games.length, learningPathId]
  );

  if (!caseLearningTest || !content)
  {
    return null;
  }

  const currentGame = games[currentGameIndex];
  const currentGameIndexInFullTextTasksJson = currentGame?.indexInFullTextTasksJson || 0;

  const renderedCaseContent = areAllGamesCompleted
    ? content
    : {
        ...content,
        json: {
          ...content?.json,
          content: content?.json?.content?.slice(0, (currentGameIndexInFullTextTasksJson || 0) + 1),
        },
      };

  let finalTitle = title;
  if (onlyShowIncorrectGames)
  {
    finalTitle = `Falsche ${title} (${incorrectCompletedGameIds.length})`;
  }

  return (
    <Drawer
      lockScroll={true}
      opened={isOpened}
      onClose={closeDrawer}
      title={
        <SlidingPanelTitle
          title={finalTitle}
          closeButtonAction={closeDrawer}
          subTitle={
            !onlyShowIncorrectGames && (
              <div css={styles.progressBarWrapper}>
                {Array.from({ length: games.length }).map((_, index) => (
                  <div key={index} css={styles.progressBarItem(completedGamesCount > index, games.length)} />
                ))}
              </div>
            )
          }
        />
      }
      position="right"
      withCloseButton={false}
      size={740}
      scrollAreaComponent={ScrollArea.Autosize}
      styles={styles.drawerStyles()}>
      <div css={styles.contentWrapper}>
        <div style={{ width: "100%" }}>
          <Richtext
            data={renderedCaseContent}
            stylesOverwrite={styles.richTextWrapper}
            richTextOverwrite={{
              documentLink: documentLinkOverwrite,
              paragraph: richTextParagraphOverwrite,
            }}
          />
          {areAllGamesCompleted && !onlyShowIncorrectGames && (
            <div css={styles.wrapper}>
              <Title order={1}>Gut gemacht!</Title>
              <div>Du hast alle Aufgaben des Tests abgeschlossen! Du kannst den Test nun abschließen.</div>
              <div css={styles.buttonWrapper}>
                <Button<"button"> styleType={"secondarySimple"} onClick={closeDrawer}>
                  Test schließen
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>
    </Drawer>
  );
};

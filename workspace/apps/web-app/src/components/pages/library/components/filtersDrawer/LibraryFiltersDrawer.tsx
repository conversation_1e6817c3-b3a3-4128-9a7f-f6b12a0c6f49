/* eslint-disable max-lines */
/* eslint-disable @typescript-eslint/no-use-before-define */
import { Trash } from "~/components/Icons/Trash.tsx";
import SlidingPanelTitle from "~/components/molecules/slidingPanelTitle/SlidingPanelTitle.tsx";
import {
  type CasesWithProgress,
  type ArticlesWithReadStatus,
} from "~/components/pages/library/utils/contentProgress.ts";
import { type FilterOption, useLibraryFiltersStore, type LibraryFiltersStore } from "~/stores/libraryFilters.store.ts";

import { findIntersection, getDistinctItemsByKey } from "@constellatio/utils/array";
import { getIsValidKey, mapToObject, objectKeys } from "@constellatio/utils/object";
import { Drawer } from "@mantine/core";
import { type FunctionComponent, useEffect, useMemo } from "react";
import { useStoreWithEqualityFn } from "zustand/traditional";

import { FilterCategory } from "./filterCategory/FilterCategory.tsx";
import * as styles from "./LibraryFiltersDrawer.styles.ts";
import { getFilterLabels, itemValuesToFilterOptions, getFilterOptions } from "./LibraryFiltersDrawer.utils.ts";

export type LibraryOverviewFiltersDrawerProps = {
  readonly articles: ArticlesWithReadStatus;
  readonly cases: CasesWithProgress;
  readonly filtersStore: LibraryFiltersStore;
};

export const LibraryOverviewFiltersDrawer: FunctionComponent<
  Pick<LibraryOverviewFiltersDrawerProps, "articles" | "cases">
> = (props) =>
{
  const libraryFiltersStore = useStoreWithEqualityFn(useLibraryFiltersStore);

  return <LibraryOverviewFiltersDrawerContent {...props} filtersStore={libraryFiltersStore} />;
};

const LibraryOverviewFiltersDrawerContent: FunctionComponent<LibraryOverviewFiltersDrawerProps> = ({
  articles,
  cases,
  filtersStore,
}) =>
{
  const {
    clearAllFilters,
    clearInvalidFilters,
    closeDrawer,
    filters: filtersMap,
    getTotalFiltersCount,
    isDrawerOpened,
  } = filtersStore;

  const filtersObject = useMemo(() => mapToObject(filtersMap), [filtersMap]);
  const filterKeys = objectKeys(filtersObject);
  const totalFiltersCount = getTotalFiltersCount();

  const availableFilterOptions: {
    [K in keyof typeof filtersObject]-?: FilterOption[];
  } = useMemo(() =>
  {
    const allLegalAreas = getFilterOptions(filtersObject, "legalArea", [...articles, ...cases]);
    const intersectionLegalAreas = findIntersection(allLegalAreas, "id");
    const distinctLegalAreas = getDistinctItemsByKey(intersectionLegalAreas, "id");
    const legalAreasFilterOptions = itemValuesToFilterOptions(distinctLegalAreas);

    const allTags = getFilterOptions(filtersObject, "tags", [...articles, ...cases]);
    const intersectionTags = findIntersection(allTags, "id");
    const distinctTags = getDistinctItemsByKey(intersectionTags, "id");
    const tagsFilterOptions = itemValuesToFilterOptions(distinctTags);

    const allTopics = getFilterOptions(filtersObject, "topic", [...articles, ...cases]);
    const intersectionTopics = findIntersection(allTopics, "id");
    const distinctTopics = getDistinctItemsByKey(intersectionTopics, "id");
    const topicsFilterOptions = itemValuesToFilterOptions(distinctTopics);

    let progressStateFilterOptions: FilterOption[] = [];
    let hasReadFilterOptions: FilterOption[] = [];
    let wasSeenFilterOptions: FilterOption[] = [];

    const allProgressStates = getFilterOptions(filtersObject, "progressStateFilterable", cases);
    const intersectionProgressStates = findIntersection(allProgressStates, "value");
    progressStateFilterOptions = getDistinctItemsByKey(intersectionProgressStates, "value");

    const allHasReadStatuses = getFilterOptions(filtersObject, "hasReadFilterable", articles);
    const intersectionHasReadStatuses = findIntersection(allHasReadStatuses, "value");
    hasReadFilterOptions = getDistinctItemsByKey(intersectionHasReadStatuses, "value");

    const allWasSeenStatuses = getFilterOptions(filtersObject, "wasSeenFilterable", articles);
    const intersectionWasSeenStatuses = findIntersection(allWasSeenStatuses, "value");
    wasSeenFilterOptions = getDistinctItemsByKey(intersectionWasSeenStatuses, "value");

    return {
      hasReadFilterable: hasReadFilterOptions,
      legalArea: legalAreasFilterOptions,
      progressStateFilterable: progressStateFilterOptions,
      tags: tagsFilterOptions,
      topic: topicsFilterOptions,
      wasSeenFilterable: wasSeenFilterOptions,
    };
  }, [filtersObject, articles, cases]);

  // when the filter options change, we need to clear the filters that are not valid anymore
  useEffect(() =>
  {
    clearInvalidFilters(availableFilterOptions);
  }, [clearInvalidFilters, availableFilterOptions]);

  return (
    <Drawer
      lockScroll={false}
      opened={isDrawerOpened}
      onClose={closeDrawer}
      withCloseButton={false}
      position="right"
      keepMounted={true}
      size="550px"
      styles={styles.drawerStyles()}
      title={
        <SlidingPanelTitle
          title="Filter"
          number={totalFiltersCount}
          variant="rich"
          closeButtonAction={closeDrawer}
          actionButton={{
            disabled: totalFiltersCount === 0,
            icon: <Trash />,
            onClick: clearAllFilters,
            title: "Alle zurücksetzen",
          }}
        />
      }>
      {filterKeys.map((filterKey) =>
      {
        if (!getIsValidKey(filtersObject, filterKey))
        {
          return null;
        }

        const filter = filtersObject[filterKey];
        const { searchesFor, title } = getFilterLabels(filterKey);

        return (
          <FilterCategory
            key={filterKey}
            items={availableFilterOptions[filterKey].map((filterOption) => ({
              ...filterOption,
              isChecked: filter.filterOptions.some((s) => s.value === filterOption.value) ?? false,
              toggle: () => filter.toggleFilter(filterOption),
            }))}
            clearFilters={() => filter.clearFilters()}
            activeFiltersCount={filter.filterOptions.length}
            title={title}
            searchesFor={searchesFor}
            isDrawerOpen={isDrawerOpened}
          />
        );
      })}
    </Drawer>
  );
};

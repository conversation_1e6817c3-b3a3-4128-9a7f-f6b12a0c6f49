/* eslint-disable max-lines */
import { type TipTapEditorRef } from "~/components/editor/tiptap-editor.tsx";
import { Button } from "~/components/ui/button.tsx";
import {
  Dialog,
  DialogBody,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "~/components/ui/dialog.tsx";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "~/components/ui/form.tsx";
import { Tabs, TabsList, TabsTrigger } from "~/components/ui/tabs.tsx";
import { cn } from "~/lib/utils.ts";

import {
  upsertFlashcardSchema,
  type UpsertFlashcardSchema,
} from "@constellatio/schemas/routers/flashcards/upsertFlashcard.schema";
import { zodResolver } from "@hookform/resolvers/zod";
import { useNavigate, useSearch } from "@tanstack/react-router";
import { Maximize2, Minimize2 } from "lucide-react";
import { type FunctionComponent, useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useFieldArray, useForm, useWatch } from "react-hook-form";

import FlashcardClozeForm from "./FlashcardClozeForm.tsx";
import FlashcardProblemForm from "./FlashcardProblemForm.tsx";
import FlashcardSchemaForm from "./FlashcardSchemaForm.tsx";
import FlashcardStandardForm from "./FlashcardStandardForm.tsx";
import useUpsertFlashcard from "../../hooks/mutations/useUpsertFlashcard.ts";
import { useFlashcardTypeSwitch } from "../../hooks/useFlashcardTypeSwitch.ts";
import {
  type CreateFlashcard,
  type EditFlashcard,
  type FlashcardEditorStore,
  useFlashcardEditorStore,
} from "../../stores/flashcardEditor.store.ts";
import { FLASHCARD_TYPE, type FlashcardType } from "../common/constants/constants.ts";
import { TagsMultiSelect } from "../common/FlashcardPage/FlashcardPageContent/TagsMultiSelect.tsx";
import { FlashcardsSetsMultiSelect } from "../common/FlashcardsSetsMultiSelect.tsx";
import { FlashcardTypeSwitchDialog } from "../common/FlashcardTypeSwitchDialog.tsx";

type FormData = UpsertFlashcardSchema;

type FlashcardUpsertDialogNewVersionProps = Pick<
  FlashcardEditorStore,
  "closeEditor" | "toggleSet" | "toggleTag" | "addSet"
> & {
  readonly editorState: EditFlashcard | CreateFlashcard;
};

export const FlashcardUpsertDialogNewVersion: FunctionComponent<FlashcardUpsertDialogNewVersionProps> = ({
  closeEditor,
  editorState,
  toggleSet,
  toggleTag,
}) =>
{
  const getDefaultFormData = useCallback(
    (type: FlashcardType): FormData =>
    {
      if (editorState.state === "edit")
      {
        return {
          ...editorState.originalFlashcard,
          content: {
            ...editorState.originalFlashcard.content,
          },
          setIds: editorState.originalFlashcard.sets.map((set) => set.id),
          tagIds: editorState.originalFlashcard.tags.map((tag) => tag.id),
        };
      }

      const baseData = {
        clozeDeletionCardId: undefined,
        clozeDeletionIndex: undefined,
        id: undefined,
        setIds: [],
        tagIds: [],
      };

      switch (type)
      {
        case FLASHCARD_TYPE.STANDARD:
          return {
            ...baseData,
            content: {
              answer: "",
              question: "",
              type: FLASHCARD_TYPE.STANDARD,
            },
          };
        case FLASHCARD_TYPE.PROBLEM:
          return {
            ...baseData,
            content: {
              case: undefined,
              // TODO: aktuell als workaround appenden wir die erste Meinung direkt in der component
              opinions: [
                { conArguments: undefined, content: "", highlight: false, proArguments: undefined, title: "" },
              ],
              problem: "",
              sourceNotes: undefined,
              type: FLASHCARD_TYPE.PROBLEM,
            },
          };
        case FLASHCARD_TYPE.CLOZE_DELETION:
          return {
            ...baseData,
            content: {
              additionalInfo: "",
              clozeDeletion: "",
              type: FLASHCARD_TYPE.CLOZE_DELETION,
            },
          };
        case FLASHCARD_TYPE.SCHEMA:
          return {
            ...baseData,
            content: {
              legalConsequence: undefined,
              sourceNotes: undefined,
              steps: [{ notes: [], subSteps: [], title: "" }],
              title: "",
              type: FLASHCARD_TYPE.SCHEMA,
            },
          };
      }
    },
    [editorState.originalFlashcard, editorState.state]
  );

  const form = useForm<FormData>({
    defaultValues: getDefaultFormData(editorState.flashcard.content.type),
    delayError: 200,
    mode: "all",
    reValidateMode: "onChange",
    resolver: zodResolver(upsertFlashcardSchema),
  });
  const stepsFieldArray = useFieldArray({
    control: form.control,
    name: "content.steps",
  });
  const opinionsFieldArray = useFieldArray({
    control: form.control,
    name: "content.opinions",
  });
  // const watchedValues = useWatch({ control: form.control });
  // const formErrors = form.formState.errors;

  const questionEditorRef = useRef<TipTapEditorRef>(null);
  const clozeDeletionEditorRef = useRef<TipTapEditorRef>(null);

  const { isPending, mutateAsync: upsertFlashcard } = useUpsertFlashcard();
  const {
    handleNoTransfer,
    handleTransfer,
    handleTypeChange,
    handleTypeSwitchDialogClose,
    isTypeSwitchDialogOpen,
    pendingTypeChange,
  } = useFlashcardTypeSwitch({ form });

  const handleFormReset = useCallback(
    ({ keepSets = true, keepTags = true }: { keepSets: boolean; keepTags: boolean }) =>
    {
      const currentSetIds = form.getValues("setIds");
      const currentTagIds = form.getValues("tagIds");
      const currentType = form.getValues("content.type");

      // const resetData = {
      //   ...getDefaultFormData(currentType),
      //   setIds: keepSets ? currentSetIds : [],
      //   tagIds: keepTags ? currentTagIds : [],
      // };

      // form.reset(resetData);

      if (currentType === FLASHCARD_TYPE.STANDARD)
      {
        form.reset({
          content: {
            answer: "",
            question: "",
            type: FLASHCARD_TYPE.STANDARD,
          },
          setIds: keepSets ? currentSetIds : [],
          tagIds: keepTags ? currentTagIds : [],
        });
        questionEditorRef.current?.focus();
      }
      else if (currentType === FLASHCARD_TYPE.CLOZE_DELETION)
      {
        form.reset({
          content: {
            additionalInfo: "",
            clozeDeletion: "",
            type: FLASHCARD_TYPE.CLOZE_DELETION,
          },
          setIds: keepSets ? currentSetIds : [],
          tagIds: keepTags ? currentTagIds : [],
        });
        clozeDeletionEditorRef.current?.focus();
      }
      else if (currentType === FLASHCARD_TYPE.PROBLEM)
      {
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        form.resetField("setIds", { defaultValue: keepSets ? currentSetIds : [] });
        form.resetField("tagIds", { defaultValue: keepTags ? currentTagIds : [] });
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        form.resetField("content.type", { defaultValue: FLASHCARD_TYPE.PROBLEM });
        form.resetField("content.problem", { defaultValue: "" });
        form.resetField("content.case", { defaultValue: undefined });
        form.resetField("content.sourceNotes", { defaultValue: undefined });
        opinionsFieldArray.replace([
          { conArguments: undefined, content: "", highlight: false, proArguments: undefined, title: "" },
        ]);
      }
      else if (currentType === FLASHCARD_TYPE.SCHEMA)
      {
        form.resetField("setIds", { defaultValue: keepSets ? currentSetIds : [] });
        form.resetField("tagIds", { defaultValue: keepTags ? currentTagIds : [] });
        form.resetField("content.type", { defaultValue: FLASHCARD_TYPE.SCHEMA });
        form.resetField("content.title", { defaultValue: "" });
        form.resetField("content.legalConsequence", { defaultValue: undefined });
        form.resetField("content.sourceNotes", { defaultValue: undefined });
        stepsFieldArray.replace([{ notes: [], subSteps: [], title: "" }]);
      }
    },
    [form, opinionsFieldArray, stepsFieldArray]
  );

  const handleEditorClose = () =>
  {
    closeEditor();
    handleFormReset({ keepSets: false, keepTags: false });
  };

  const handleSubmit = async (values: FormData, shouldClose: boolean) =>
  {
    await upsertFlashcard(values, {
      shouldClose,
      tempId: editorState.flashcard.tempId,
    });
    // TODO: auch noch auslagern in hook am besten weil nur bei onSuccess korrekt!
    if (!shouldClose)
    {
      handleFormReset({ keepSets: true, keepTags: true });
    }
  };

  const isSubmitDisabled = useMemo(
    () => !form.formState.isValid || !form.formState.isDirty || isPending,
    [form.formState.isDirty, form.formState.isValid, isPending]
  );

  const [isFullscreen, setIsFullscreen] = useState(false);

  // Sets und Tags synchronisieren
  useEffect(() =>
  {
    const newSetIds = editorState.flashcard.sets.map((set) => set.id);
    const newTagIds = editorState.flashcard.tags.map((tag) => tag.id);
    const currentSetIds = form.getValues("setIds");
    const currentTagIds = form.getValues("tagIds");

    const setsChanged = JSON.stringify(newSetIds) !== JSON.stringify(currentSetIds);
    const tagsChanged = JSON.stringify(newTagIds) !== JSON.stringify(currentTagIds);

    if (setsChanged)
    {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      form.setValue("setIds", newSetIds, { shouldDirty: true, shouldValidate: true });
    }

    if (tagsChanged)
    {
      form.setValue("tagIds", newTagIds, { shouldDirty: true, shouldValidate: true });
    }
  }, [editorState.flashcard.sets, editorState.flashcard.tags, form]);

  // Content einmalig initialisieren
  useEffect(() =>
  {
    const { content } = editorState.flashcard;

    switch (content.type)
    {
      case FLASHCARD_TYPE.STANDARD:
        {
          const currentAnswer = form.getValues("content.answer");
          const currentQuestion = form.getValues("content.question");

          if (content.answer !== currentAnswer)
          {
            form.setValue("content.answer", content.answer, {
              shouldDirty: Boolean(content.answer),
              shouldValidate: Boolean(content.answer),
            });
          }

          if (content.question !== currentQuestion)
          {
            form.setValue("content.question", content.question, {
              shouldDirty: Boolean(content.question),
              shouldValidate: Boolean(content.question),
            });
          }
        }
        break;

      case FLASHCARD_TYPE.CLOZE_DELETION:
        {
          const currentClozeDeletion = form.getValues("content.clozeDeletion");
          const currentAdditionalInfo = form.getValues("content.additionalInfo");

          if (content.clozeDeletion !== currentClozeDeletion)
          {
            form.setValue("content.clozeDeletion", content.clozeDeletion, {
              shouldDirty: Boolean(content.clozeDeletion),
              shouldValidate: Boolean(content.clozeDeletion),
            });
          }

          if (content.additionalInfo !== currentAdditionalInfo)
          {
            form.setValue("content.additionalInfo", content.additionalInfo, {
              shouldDirty: Boolean(content.additionalInfo),
              shouldValidate: Boolean(content.additionalInfo),
            });
          }
        }
        break;

      case FLASHCARD_TYPE.PROBLEM:
        {
          const currentProblem = form.getValues("content.problem");
          const currentOpinions = form.getValues("content.opinions");
          const currentSourceNotes = form.getValues("content.sourceNotes");
          const currentCase = form.getValues("content.case");

          if (content.problem !== currentProblem)
          {
            form.setValue("content.problem", content.problem, {
              shouldDirty: Boolean(content.problem),
              shouldValidate: Boolean(content.problem),
            });
          }

          if (JSON.stringify(content.opinions) !== JSON.stringify(currentOpinions))
          {
            form.setValue("content.opinions", content.opinions, {
              shouldDirty: Boolean(content.opinions?.length),
              shouldValidate: Boolean(content.opinions?.length),
            });
          }

          if (content.sourceNotes !== currentSourceNotes)
          {
            form.setValue("content.sourceNotes", content.sourceNotes, {
              shouldDirty: Boolean(content.sourceNotes),
              shouldValidate: Boolean(content.sourceNotes),
            });
          }

          if (content.case !== currentCase)
          {
            form.setValue("content.case", content.case, {
              shouldDirty: Boolean(content.case),
              shouldValidate: Boolean(content.case),
            });
          }
        }
        break;

      case FLASHCARD_TYPE.SCHEMA:
        {
          const currentLegalConsequence = form.getValues("content.legalConsequence");
          const currentTitle = form.getValues("content.title");
          const currentSteps = form.getValues("content.steps");
          const currentSourceNotes = form.getValues("content.sourceNotes");

          if (content.legalConsequence !== currentLegalConsequence)
          {
            form.setValue("content.legalConsequence", content.legalConsequence, {
              shouldDirty: Boolean(content.legalConsequence),
              shouldValidate: Boolean(content.legalConsequence),
            });
          }

          if (content.title !== currentTitle)
          {
            form.setValue("content.title", content.title, {
              shouldDirty: Boolean(content.title),
              shouldValidate: Boolean(content.title),
            });
          }

          if (JSON.stringify(content.steps) !== JSON.stringify(currentSteps))
          {
            form.setValue("content.steps", content.steps, {
              shouldDirty: Boolean(content.steps?.length),
              shouldValidate: Boolean(content.steps?.length),
            });
          }

          if (content.sourceNotes !== currentSourceNotes)
          {
            form.setValue("content.sourceNotes", content.sourceNotes, {
              shouldDirty: Boolean(content.sourceNotes),
              shouldValidate: Boolean(content.sourceNotes),
            });
          }
        }
        break;
    }
    // NOTE: This effect is only needed once, so we can disable the exhaustive-deps rule
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <>
      {pendingTypeChange && (
        <FlashcardTypeSwitchDialog
          hasContent1={
            Boolean(form.getValues("content.type") === FLASHCARD_TYPE.STANDARD && form.getValues("content.question")) ||
            Boolean(
              form.getValues("content.type") === FLASHCARD_TYPE.CLOZE_DELETION &&
                form.getValues("content.clozeDeletion")
            )
          }
          hasContent2={
            Boolean(form.getValues("content.type") === FLASHCARD_TYPE.STANDARD && form.getValues("content.answer")) ||
            Boolean(
              form.getValues("content.type") === FLASHCARD_TYPE.CLOZE_DELETION &&
                form.getValues("content.additionalInfo")
            )
          }
          hasSets={editorState.flashcard.sets.length > 0}
          hasTags={editorState.flashcard.tags.length > 0}
          isOpen={isTypeSwitchDialogOpen}
          newType={pendingTypeChange}
          onClose={handleTypeSwitchDialogClose}
          onNoTransfer={handleNoTransfer}
          onTransfer={handleTransfer}
          previousType={form.getValues("content.type")}
        />
      )}
      <DialogContent className={cn("max-w-3xl", isFullscreen && "h-[99svh] max-w-[98svw]")}>
        <DialogHeader className="relative">
          <div className="absolute -top-4 right-2">
            <Button className="size-8" onClick={() => setIsFullscreen(!isFullscreen)} size="icon" variant="ghost">
              {isFullscreen ? <Minimize2 className="size-4" /> : <Maximize2 className="size-4" />}
            </Button>
          </div>
          <DialogTitle className="font-sans text-xl">
            {editorState.state === "edit" ? "Karteikarte bearbeiten" : "Neue Karteikarte erstellen"}
          </DialogTitle>
          <DialogDescription>
            {editorState.state === "edit" ? (
              "Bearbeite deine Karteikarte hier."
            ) : (
              <>
                Drücke nach dem Ausfüllen auf &quot;Erstellen&quot; wenn du weitere Karteikarten erstellen willst.
                <br />
                Drücke auf &quot;Erstellen und schließen&quot;, wenn du fertig bist.
              </>
            )}
          </DialogDescription>
        </DialogHeader>
        <DialogBody className={cn("flex flex-col gap-8", isFullscreen && "flex-1 px-6")}>
          <Form {...form}>
            <form className="space-y-8" noValidate>
              <FormField
                control={form.control}
                name="content.type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel htmlFor="flashcard-type">Wähle den Karteikarten-Typ</FormLabel>
                    <FormControl id="flashcard-type">
                      <Tabs
                        className="w-full"
                        onValueChange={(value) => handleTypeChange(value as FlashcardType)}
                        value={field.value}>
                        <TabsList className="w-full rounded-full bg-muted-4 px-2">
                          <div className="flex-1">
                            <TabsTrigger
                              className="w-full flex-1 rounded-full px-6"
                              disabled={editorState.state === "edit"}
                              value={FLASHCARD_TYPE.STANDARD}>
                              Standard
                            </TabsTrigger>
                          </div>
                          <div className="flex-1">
                            <TabsTrigger
                              className="w-full flex-1 rounded-full px-6"
                              disabled={editorState.state === "edit"}
                              value={FLASHCARD_TYPE.CLOZE_DELETION}>
                              Lückentext
                            </TabsTrigger>
                          </div>
                          <div className="flex-1">
                            <TabsTrigger
                              className="w-full flex-1 rounded-full px-6"
                              disabled={editorState.state === "edit"}
                              value={FLASHCARD_TYPE.PROBLEM}>
                              Problem
                            </TabsTrigger>
                          </div>
                          <div className="flex-1">
                            <TabsTrigger
                              className="w-full flex-1 rounded-full px-6"
                              disabled={editorState.state === "edit"}
                              value={FLASHCARD_TYPE.SCHEMA}>
                              Schema
                            </TabsTrigger>
                          </div>
                        </TabsList>
                      </Tabs>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              {/* Multi-Select Grid */}
              <div className={cn("grid grid-cols-1 gap-4", isFullscreen && "lg:grid-cols-2")}>
                <FormField
                  control={form.control}
                  name="setIds"
                  render={() => (
                    <FormItem>
                      <FormLabel htmlFor="flashcard-sets">Zu Set(s) zuweisen *</FormLabel>
                      <FormControl id="flashcard-sets">
                        <FlashcardsSetsMultiSelect
                          onFlashcardsSetToggle={toggleSet}
                          selectedFlashcardsSets={editorState.flashcard.sets}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="tagIds"
                  render={() => (
                    <FormItem>
                      <FormLabel htmlFor="flashcard-tags">Tags zuweisen</FormLabel>
                      <FormControl id="flashcard-tags">
                        <TagsMultiSelect
                          onTagToggle={toggleTag}
                          selectedTags={editorState.flashcard.tags}
                          showExternalPills={false}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              {form.getValues("content.type") === FLASHCARD_TYPE.STANDARD && (
                <FlashcardStandardForm form={form} questionEditorRef={questionEditorRef} isFullscreen={isFullscreen} />
              )}
              {form.getValues("content.type") === FLASHCARD_TYPE.CLOZE_DELETION && (
                <FlashcardClozeForm
                  form={form}
                  clozeDeletionEditorRef={clozeDeletionEditorRef}
                  isFullscreen={isFullscreen}
                />
              )}
              {form.getValues("content.type") === FLASHCARD_TYPE.PROBLEM && (
                <FlashcardProblemForm form={form} isFullscreen={isFullscreen} opinionsFieldArray={opinionsFieldArray} />
              )}
              {form.getValues("content.type") === FLASHCARD_TYPE.SCHEMA && (
                <FlashcardSchemaForm form={form} isFullscreen={isFullscreen} stepsFieldArray={stepsFieldArray} />
              )}
            </form>
          </Form>
        </DialogBody>

        <DialogFooter>
          <Button onClick={handleEditorClose} variant="secondary">
            Abbrechen
          </Button>
          <Button
            disabled={isSubmitDisabled}
            onClick={form.handleSubmit(async (e) => handleSubmit(e, editorState.state === "edit"))}
            type="submit">
            {editorState.state === "edit" ? "Änderungen speichern" : "Erstellen"}
          </Button>
          {editorState.state === "create" && (
            <Button
              disabled={isSubmitDisabled}
              onClick={form.handleSubmit(async (e) => handleSubmit(e, true))}
              type="submit">
              Erstellen und schließen
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </>
  );
};

export const FlashcardUpsertDialogNewVersionWrapper: FunctionComponent = () =>
{
  const { addSet, closeEditor, editorState, toggleSet, toggleTag } = useFlashcardEditorStore();
  const search = useSearch({ from: "/_authed/_onboarded/_free/flashcards/my-cards", shouldThrow: false });
  const navigate = useNavigate();

  const closeDialog = () =>
  {
    closeEditor();

    if (search?.cardId)
    {
      void navigate({ replace: true, search: { cardId: undefined }, to: "/flashcards/my-cards" });
    }
  };

  return (
    <Dialog onOpenChange={closeDialog} open={editorState.state !== "closed"}>
      {editorState.state === "closed" ? null : (
        <FlashcardUpsertDialogNewVersion
          addSet={addSet}
          closeEditor={closeDialog}
          editorState={editorState}
          toggleSet={toggleSet}
          toggleTag={toggleTag}
        />
      )}
    </Dialog>
  );
};

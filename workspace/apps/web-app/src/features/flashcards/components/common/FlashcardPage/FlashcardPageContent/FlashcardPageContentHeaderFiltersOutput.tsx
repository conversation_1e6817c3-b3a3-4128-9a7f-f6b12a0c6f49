import { Badge } from "~/components/ui/badge.tsx";

import { type FilterItem } from "@constellatio/schemas/common/flashcards/filter.validation";
import { Tag, X } from "lucide-react";
import { type FunctionComponent } from "react";

export interface FlashcardPageContentHeaderFiltersOutputProps
{
  readonly onTagRemove: (tag: FilterItem) => void;
  readonly selectedTags: FilterItem[];
}

export const FlashcardPageContentHeaderFiltersOutput: FunctionComponent<
  FlashcardPageContentHeaderFiltersOutputProps
> = ({ onTagRemove, selectedTags }) =>
{
  const selectedTagBadges = selectedTags
    .map((tag) =>
    {
      return (
        <Badge
          // FIXME: noch nicht gelöst, macht andere styles drauf
          as={"button"}
          key={tag.id}
          onClick={() => onTagRemove(tag)}
          size="md"
          variant="muted">
          {tag.name}
          <X size={16} />
        </Badge>
      );
    })
    .filter(Boolean);

  if (selectedTags.length === 0)
  {
    return null;
  }

  return <div className="flex flex-wrap gap-2 py-2">{selectedTagBadges}</div>;
};

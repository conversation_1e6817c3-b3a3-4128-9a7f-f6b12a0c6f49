/* eslint-disable max-lines */

import { Badge } from "~/components/ui/badge.tsx";
import { InfiniteScrollTrigger } from "~/components/ui/infinite-scroll-trigger.tsx";
import { CreateFlashcard } from "~/flashcards/components/common/CreateFlashcardCard/CreateFlashcardCard.tsx";
import { FlashcardPageContent } from "~/flashcards/components/common/FlashcardPage/FlashcardPageContent/FlashcardPageContent.tsx";
import { FlashcardPageContentSection } from "~/flashcards/components/common/FlashcardPage/FlashcardPageContent/FlashcardPageContentSection.tsx";
import { StartLearningCard } from "~/flashcards/components/common/StartLearningCard/StartLearningCard.tsx";
import { StatisticCard } from "~/flashcards/components/common/StatisticCard/StatisticCard.tsx";
import { FlashcardsSetCard } from "~/flashcards/components/FlashcardsSet/FlashcardsSetCard/FlashcardsSetCard.tsx";
import { FlashcardsSetsSkeleton } from "~/flashcards/components/FlashcardsSet/FlashcardsSetCard/FlashcardsSetCardSkeleton.tsx";
import { FlashcardsSetDeleteDialog } from "~/flashcards/components/FlashcardsSet/FlashcardsSetDeleteDialog.tsx";
import { useGetAllFlashcardsSetsForOverview } from "~/flashcards/hooks/queries/useGetAllFlashcardsSetsForOverview.ts";
import { useGetAllStarredFlashcardsSets } from "~/flashcards/hooks/queries/useGetAllStarredFlashcardsSets.ts";
import useGetFlashcardStatistics from "~/flashcards/hooks/queries/useGetFlashcardStatistics.ts";
import { useStartPageFlashcardsSetStore } from "~/flashcards/stores/flashcardsSet.store.ts";
import { useFlashcardsSetEditorStore } from "~/flashcards/stores/flashcardsSetEditor.store.ts";
import { useInfiniteScroll } from "~/hooks/useInfiniteScroll.ts";
import { usePageTitleUpdate } from "~/hooks/usePageTitleUpdate.ts";
import { getFlashcardEntityCountText } from "~/utils/flashcards-translations.ts";

import {
  getFlashcardsSetsInitialPageSize,
  getFlashcardsSetsLoadMorePageSize,
} from "@constellatio/backend/utils/flashcards";
import { Brain, Clock, GraduationCap } from "lucide-react";
import { Fragment } from "react";

const Start = () =>
{
  usePageTitleUpdate("Karteikarten - Startseite");

  const {
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading,
    sets: setsWithDueCards,
  } = useGetAllFlashcardsSetsForOverview({
    deletionState: "notDeleted",
    filter: {
      debouncedSearch: "",
      sortBy: "updatedAt",
      tags: [],
    },
    shouldOnlyGetSetsWithDueCards: true,
  });

  const { ref: loadMoreTriggerRef } = useInfiniteScroll({
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  });

  const { data: starredSets } = useGetAllStarredFlashcardsSets();
  const { statistics } = useGetFlashcardStatistics();
  const store = useStartPageFlashcardsSetStore();

  const {
    clearSelection,
    currentSelection: { allSelected, selectionFilters: filter, selectionIds: selectedItems },
    getIsDialogOpen,
    handleActionWithDialog,
    isSelectModeActive,
    setIsDialogOpen,
    setSingleSelectedId,
    singleSelectedId,
    toggleIsItemSelected: toggleSelection,
  } = store;

  const { handleUpsertFlashcardsSet } = useFlashcardsSetEditorStore();

  return (
    <Fragment>
      <div className="space-y-8 py-8">
        <StartLearningCard />
      </div>
      <div className="space-y-8 py-2">
        <CreateFlashcard />
      </div>
      <div className="space-y-8 py-8">
        <div className="space-y-4">
          <h2 className="text-lg font-medium">Deine Statistiken</h2>
          <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
            <StatisticCard cardCount={statistics.repetitionsTotal} icon={Brain} title="Wiederholungen insgesamt" />
            <StatisticCard cardCount={statistics.dueCards} icon={Clock} title="Fällige Karten" />
            <StatisticCard
              cardCount={statistics.unlearnedCards}
              icon={GraduationCap}
              title="Noch nicht gelernte Karten"
            />
          </div>
        </div>
      </div>
      <div className="py-4">
        <div className="flex items-center gap-2">
          <h2 className="text-lg font-medium">Deine zu lernenden Sets</h2>
          <Badge variant="muted">
            {getFlashcardEntityCountText({
              count: setsWithDueCards.length,
              type: "flashcardsSet",
            })}
          </Badge>
        </div>
      </div>
      <FlashcardPageContent>
        <FlashcardPageContentSection
          emptyStateComponent={<div>Keine fälligen oder ungelernten Karten vorhanden</div>}
          itemsLoading={isLoading}
          loadedItemsCount={setsWithDueCards.length}
          skeletonComponent={<FlashcardsSetsSkeleton amountOfSkeletonItems={getFlashcardsSetsInitialPageSize} />}>
          <Fragment>
            {setsWithDueCards.map((flashcardsSet) => (
              <FlashcardsSetCard
                allSelected={allSelected}
                cardInModal={false}
                deletionState={"notDeleted"}
                dueCardsCount={flashcardsSet.dueCardsCount}
                flashcardsSet={flashcardsSet}
                isSelected={selectedItems.has(flashcardsSet.id)}
                isSelectModeActive={isSelectModeActive}
                isStarred={starredSets?.some((starredSet) => starredSet.id === flashcardsSet.id) ?? false}
                key={flashcardsSet.id}
                onDelete={() => handleActionWithDialog("delete", flashcardsSet.id)}
                onDeleteWithFlashcards={() => handleActionWithDialog("deleteWithFlashcards", flashcardsSet.id)}
                onEdit={() =>
                  handleUpsertFlashcardsSet({
                    ...flashcardsSet,
                    description: flashcardsSet.description ?? undefined,
                  })
                }
                onToggleSelection={toggleSelection}
                setId={flashcardsSet.id}
                shouldOnlyGetSetsWithDueCards={true}
                showDueCardsCount={false}
                unlearnedCardsCount={flashcardsSet.unlearnedCardsCount}
              />
            ))}
          </Fragment>
          <InfiniteScrollTrigger
            isLoading={isFetchingNextPage}
            loadingComponent={<FlashcardsSetsSkeleton amountOfSkeletonItems={getFlashcardsSetsLoadMorePageSize} />}
            ref={loadMoreTriggerRef}
          />
        </FlashcardPageContentSection>
      </FlashcardPageContent>
      <FlashcardsSetDeleteDialog
        allSelected={allSelected}
        clearSelection={clearSelection}
        filter={filter}
        isDeleteDialogOpen={getIsDialogOpen("delete")}
        moveFlashcardsToTrash={false}
        selectedItems={selectedItems}
        setIsDialogOpen={(open) => setIsDialogOpen({ open, type: "delete" })}
        setSingleSelectedId={setSingleSelectedId}
        singleSelectedId={singleSelectedId}
        totalCount={setsWithDueCards.length}
      />
      <FlashcardsSetDeleteDialog
        allSelected={allSelected}
        clearSelection={clearSelection}
        filter={filter}
        isDeleteDialogOpen={getIsDialogOpen("deleteWithFlashcards")}
        moveFlashcardsToTrash={true}
        redirectToOverviewPage={undefined}
        selectedItems={selectedItems}
        setIsDialogOpen={(isOpen) => setIsDialogOpen({ open: isOpen, type: "deleteWithFlashcards" })}
        setSingleSelectedId={setSingleSelectedId}
        singleSelectedId={singleSelectedId}
        totalCount={setsWithDueCards.length}
      />
    </Fragment>
  );
};

export default Start;

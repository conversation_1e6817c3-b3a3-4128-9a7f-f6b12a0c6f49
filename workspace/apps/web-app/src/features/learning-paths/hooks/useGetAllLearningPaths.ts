import useCasesProgress from "~/hooks/useCasesProgress.ts";
import useGetAllLearningPathsProgress from "~/hooks/useGetAllLearningPathsProgress.ts";
import { useReadArticles } from "~/hooks/useReadArticles.ts";
import { useSeenArticles } from "~/hooks/useSeenArticles.ts";
import { api } from "~/utils/trpc.ts";

import { type RouterOutputs } from "@constellatio/backend/trpc";
import { type LearningPathWithExtraData } from "@constellatio/cms/app/utils/learningPaths";
import { useQuery } from "@tanstack/react-query";
import { useMemo } from "react";

export type UnitStatus = "not-started" | "in-progress" | "completed" | "blocked";
export type LearningPathStatus = "not-started" | "in-progress" | "completed";

interface UseGetAllLearningPathsResult
{
  isLoading: boolean;
  learningPaths: Array<
    LearningPathWithExtraData & {
      status: LearningPathStatus;
      unitsCompletedCount: number;
      unitsStatus: UnitStatus[];
    }
  >;
}

const useGetAllLearningPaths = (): UseGetAllLearningPathsResult =>
{
  const { data: allLearningPaths, isLoading: isLoadingLearningPaths } = useQuery(
    api.learningPath.getLearningPaths.queryOptions()
  );
  const { data: casesProgress, isPending: isCasesProgressPending } = useCasesProgress();
  const { data: seenArticles, isPending: isSeenArticlesPending } = useSeenArticles();
  const { data: readArticles, isPending: isReadArticlesPending } = useReadArticles();
  const { isLoading: isCompletionsPending, progress: allCompletions } = useGetAllLearningPathsProgress();
  const completionsMap = useMemo(() =>
  {
    const map: Record<string, RouterOutputs["learningPath"]["getAllLearningPathsProgress"]> = {};
    if (!allCompletions) return map;
    for (const completion of allCompletions)
    {
      if (!completion.learningPathId) continue;
      if (!map[completion.learningPathId])
      {
        map[completion.learningPathId] = [];
      }
      map[completion.learningPathId]!.push(completion);
    }
    return map;
  }, [allCompletions]);

  const learningPaths = useMemo(() =>
  {
    if (!allLearningPaths || !casesProgress || !seenArticles || !completionsMap || !readArticles) return [];

    return allLearningPaths.map((learningPath) =>
    {
      const { allUnits } = learningPath;
      const progressData = completionsMap[learningPath.id!] || [];
      const unitsStatus: UnitStatus[] = allUnits.map((unit) =>
      {
        const contentPieces = unit.contentPieces.filter(Boolean);
        const casesInUnit = contentPieces.filter((c) => c.__typename === "Case");
        const articlesInUnit = contentPieces.filter((c) => c.__typename === "Article");

        // Early Return für Learning Test
        if (unit.caseLearningTests && unit.caseLearningTests.length > 0)
        {
          const testId = unit.caseLearningTests[0]?.id;
          const testProgress = progressData.find((progress) => progress.learningPathUnitTestId === testId);
          if (testProgress)
          {
            if (testProgress.completedAt == null)
            {
              return "in-progress";
            }
            else
            {
              return "completed";
            }
          }
        }

        let testStatus: UnitStatus | null = null;

        const casesStatus = casesInUnit.map((c) =>
        {
          const progress = casesProgress.find((p) => p.caseId === c.id)?.progressState;
          return { id: c.id, status: progress };
        });

        const articlesStatus = articlesInUnit.map((a) =>
        {
          if (!a.id) throw new Error("Article has no id");
          const hasRead = readArticles.includes(a.id);
          return { id: a.id, status: hasRead ? "completed" : "not-started" };
        });

        if (unit.caseLearningTests && unit.caseLearningTests.length > 0)
        {
          const testId = unit.caseLearningTests[0]?.id;
          const testCompleted = progressData.some(
            (progress) => progress.learningPathUnitTestId === testId && progress.completedAt != null
          );
          testStatus = testCompleted ? "completed" : "not-started";
        }

        const casesStatusArr = casesStatus.map((c) =>
          c.status === "completed"
            ? "completed"
            : c.status === "completing-tests" || c.status === "solving-case"
              ? "in-progress"
              : "not-started"
        );

        const articlesStatusArr = articlesStatus.map((a) => a.status);
        const allStatuses = [...casesStatusArr, ...articlesStatusArr];
        if (testStatus) allStatuses.push(testStatus);

        // Debug-Ausgaben pro Unit
        // console.log(`Unit #${idx + 1} in ${learningPath.title || learningPath.id}`);
        // console.log("Cases:", casesStatus);
        // console.log("Articles:", articlesStatus);
        // console.log("TestStatus:", testStatus);
        // console.log("allStatuses:", allStatuses);

        if (allStatuses.every((s) => s === "completed"))
        {
          return "completed";
        }
        if (allStatuses.every((s) => s === "not-started"))
        {
          return "not-started";
        }
        if (allStatuses.some((s) => s === "in-progress" || s === "completed"))
        {
          return "in-progress";
        }
        return "not-started";
      });

      const firstNotCompletedIdx = unitsStatus.findIndex((s) => s !== "completed");
      if (firstNotCompletedIdx !== -1)
      {
        for (let i = firstNotCompletedIdx + 1; i < unitsStatus.length; i++)
        {
          unitsStatus[i] = "blocked";
        }
      }

      const unitsCompletedCount = unitsStatus.filter((s) => s === "completed").length;

      // Debug-Ausgaben für den Lernpfad
      // console.log(`Lernpfad: ${learningPath.title || learningPath.id}`);
      // console.log("unitsStatus:", unitsStatus);
      // console.log("unitsCompletedCount:", unitsCompletedCount, "/", allUnits.length);

      const nonBlockedUnits = unitsStatus.filter((s) => s !== "blocked");
      let status: LearningPathStatus = "not-started";
      if (nonBlockedUnits.length > 0)
      {
        if (nonBlockedUnits.every((s) => s === "completed"))
        {
          status = "completed";
        }
        else if (nonBlockedUnits.some((s) => s === "in-progress" || s === "completed"))
        {
          status = "in-progress";
        }
        else
        {
          status = "not-started";
        }
      }
      // Logge die finale Statusentscheidung
      // console.log("Lernpfad-Status (final):", status);
      return {
        ...learningPath,
        status,
        unitsCompletedCount,
        unitsStatus,
      };
    });
  }, [allLearningPaths, casesProgress, seenArticles, completionsMap, readArticles]);

  // Gesamtergebnis loggen
  // console.log("learningPaths (final):", learningPaths);

  return {
    isLoading:
      isLoadingLearningPaths ||
      isCasesProgressPending ||
      isSeenArticlesPending ||
      isReadArticlesPending ||
      isCompletionsPending,
    learningPaths,
  };
};

export default useGetAllLearningPaths;

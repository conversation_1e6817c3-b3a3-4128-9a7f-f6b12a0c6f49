import { InvalidateQueriesContext } from "~/providers/InvalidateQueriesProvider.tsx";
import { api } from "~/utils/trpc.ts";

import { useMutation, useQueryClient } from "@tanstack/react-query";

import useContextAndErrorIfNull from "./useContextAndErrorIfNull.ts";

const useAddBookmark = () =>
{
  const queryClient = useQueryClient();
  const { invalidateBookmarks } = useContextAndErrorIfNull(InvalidateQueriesContext);

  /**
   * CAUTION: onError MUST come after onMutate or type inference for "context" will fail!
   */

  return useMutation(
    api.bookmarks.addBookmark.mutationOptions({
      onMutate: async (newBookmark) =>
      {
        await queryClient.cancelQueries({
          queryKey: api.bookmarks.getAllBookmarks.queryKey(),
        });
        const previousBookmarks = queryClient.getQueryData(api.bookmarks.getAllBookmarks.queryKey());
        queryClient.setQueryData(api.bookmarks.getAllBookmarks.queryKey(), (oldBookmarks = []) => [
          ...oldBookmarks,
          newBookmark,
        ]);

        return { previousBookmarks: previousBookmarks ?? [] };
      },
      onError: (err, newBookmark, context) =>
      {
        if (context)
        {
          queryClient.setQueryData(api.bookmarks.getAllBookmarks.queryKey(), context.previousBookmarks);
        }

        console.error("Something went wrong while adding bookmark: ", {
          context,
          err,
          newBookmark,
        });
      },
      onSettled: invalidateBookmarks,
    })
  );
};

export default useAddBookmark;

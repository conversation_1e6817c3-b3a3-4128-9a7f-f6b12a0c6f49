import { InvalidateQueriesContext } from "~/providers/InvalidateQueriesProvider.tsx";
import { api } from "~/utils/trpc.ts";

import { useMutation } from "@tanstack/react-query";

import useContextAndErrorIfNull from "./useContextAndErrorIfNull.ts";

const useSetCaseProgress = () =>
{
  const { invalidateCaseProgress, invalidateCasesProgress } = useContextAndErrorIfNull(InvalidateQueriesContext);

  return useMutation(
    api.casesProgress.setProgressState.mutationOptions({
      onSuccess: async (_, { caseId }) =>
      {
        await invalidateCaseProgress({ caseId });
        await invalidateCasesProgress();
      },
    })
  );
};

export default useSetCaseProgress;

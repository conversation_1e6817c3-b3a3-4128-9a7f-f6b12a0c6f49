import { toast } from "~/components/ui/sonner.tsx";
import { api } from "~/utils/trpc.ts";

import { useMutation } from "@tanstack/react-query";

export const useStripe = () =>
{
  const { mutateAsync: generateStripeCheckoutStandardPlanSession, status } = useMutation(
    api.billing.generateStripeCheckoutStandardPlanSession.mutationOptions({
      onError(error)
      {
        console.error("error while getting stripe session url", error);
        toast.error("Bitte versuche es später erneut oder kontaktiere den Support.");
      },
    })
  );

  const { mutateAsync: generateStripeBillingPortalSession } = useMutation(
    api.billing.generateStripeBillingPortalSession.mutationOptions({
      onError: (error) =>
      {
        console.error("error while generating stripe billing portal session", error);
        toast.error("Bitte versuche es später erneut oder kontaktiere den Support.");
      },
    })
  );

  const { mutateAsync: generateStripeCheckoutLifetimeSession } = useMutation(
    api.billing.generateStripeCheckoutLifetimeSession.mutationOptions({
      onError: (error) =>
      {
        console.error("error while generating stripe checkout lifetime session", error);
        toast.error("Bitte versuche es später erneut oder kontaktiere den Support.");
      },
    })
  );

  const { mutateAsync: generateStripeCheckoutStandardPlanYearlySession } = useMutation(
    api.billing.generateStripeCheckoutStandardPlanYearlySession.mutationOptions({
      onError: (error) =>
      {
        console.error("error while generating stripe checkout standard plan yearly session", error);
        toast.error("Bitte versuche es später erneut oder kontaktiere den Support.");
      },
    })
  );

  const redirectToStripe = async (
    type:
      | "billing-portal"
      | "checkout-standard-plan"
      | "checkout-lifetime-plan"
      | "checkout-standard-yearly-plan"
      | null
  ) =>
  {
    const handleSuccess = (stripeUrl: string) =>
    {
      window.location.href = stripeUrl;
    };

    if (type === "billing-portal")
    {
      await generateStripeBillingPortalSession(undefined, {
        onSuccess: handleSuccess,
      });
    }
    else if (type === "checkout-standard-plan")
    {
      await generateStripeCheckoutStandardPlanSession(undefined, {
        onSuccess: handleSuccess,
      });
    }
    else if (type === "checkout-lifetime-plan")
    {
      await generateStripeCheckoutLifetimeSession(undefined, {
        onSuccess: handleSuccess,
      });
    }
    else if (type === "checkout-standard-yearly-plan")
    {
      await generateStripeCheckoutStandardPlanYearlySession(undefined, {
        onSuccess: handleSuccess,
      });
    }
    else
    {
      throw new Error("Invalid type");
    }
  };

  return {
    isLoading: status === "pending",
    redirectToStripe,
  };
};

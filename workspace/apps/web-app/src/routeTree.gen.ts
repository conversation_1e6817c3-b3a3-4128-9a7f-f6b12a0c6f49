/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

// Import Routes

import { Route as rootRoute } from "./routes/__root";
import { Route as RecoverImport } from "./routes/recover";
import { Route as NotAuthedRouteImport } from "./routes/_not-authed/route";
import { Route as AuthedRouteImport } from "./routes/_authed/route";
import { Route as IndexImport } from "./routes/index";
import { Route as NotAuthedRegisterImport } from "./routes/_not-authed/register";
import { Route as NotAuthedLoginImport } from "./routes/_not-authed/login";
import { Route as NotAuthedConfirmImport } from "./routes/_not-authed/confirm";
import { Route as AuthedOnboardedRouteImport } from "./routes/_authed/_onboarded/route";
import { Route as AuthedOnboardingIndexImport } from "./routes/_authed/onboarding/index";
import { Route as NotAuthedExtensionSelectionImport } from "./routes/_not-authed/extension/selection";
import { Route as NotAuthedExtensionDndImport } from "./routes/_not-authed/extension/dnd";
import { Route as AuthedOnboardedSubscribedRouteImport } from "./routes/_authed/_onboarded/_subscribed/route";
import { Route as AuthedOnboardedSubscribedLegacyRouteImport } from "./routes/_authed/_onboarded/_subscribed/_legacy/route";
import { Route as AuthedOnboardedFreeSearchRouteImport } from "./routes/_authed/_onboarded/_free/search/route";
import { Route as AuthedOnboardedFreeFlashcardsRouteImport } from "./routes/_authed/_onboarded/_free/flashcards/route";
import { Route as AuthedOnboardedFreeLegacyRouteImport } from "./routes/_authed/_onboarded/_free/_legacy/route";
import { Route as AuthedOnboardedSubscribedLearningPathsIndexImport } from "./routes/_authed/_onboarded/_subscribed/learning-paths/index";
import { Route as AuthedOnboardedFreeSearchIndexImport } from "./routes/_authed/_onboarded/_free/search/index";
import { Route as AuthedOnboardedFreeFlashcardsIndexImport } from "./routes/_authed/_onboarded/_free/flashcards/index";
import { Route as AuthedOnboardedSubscribedLegacyPersonalSpaceImport } from "./routes/_authed/_onboarded/_subscribed/_legacy/personal-space";
import { Route as AuthedOnboardedSubscribedLegacyPaymentSuccessImport } from "./routes/_authed/_onboarded/_subscribed/_legacy/payment-success";
import { Route as AuthedOnboardedSubscribedLegacyNotificationsImport } from "./routes/_authed/_onboarded/_subscribed/_legacy/notifications";
import { Route as AuthedOnboardedSubscribedLegacyConfirmEmailChangeImport } from "./routes/_authed/_onboarded/_subscribed/_legacy/confirm-email-change";
import { Route as AuthedOnboardedFreeFlashcardsMySetsImport } from "./routes/_authed/_onboarded/_free/flashcards/my-sets";
import { Route as AuthedOnboardedFreeFlashcardsMyCardsImport } from "./routes/_authed/_onboarded/_free/flashcards/my-cards";
import { Route as AuthedOnboardedFreeLegacyProfileImport } from "./routes/_authed/_onboarded/_free/_legacy/profile";
import { Route as AuthedOnboardedFreeLegacyDashboardImport } from "./routes/_authed/_onboarded/_free/_legacy/dashboard";
import { Route as AuthedOnboardedFreeFlashcardsTrashBinRouteImport } from "./routes/_authed/_onboarded/_free/flashcards/trash-bin/route";
import { Route as AuthedOnboardedFreeFlashcardsReviewRouteImport } from "./routes/_authed/_onboarded/_free/flashcards/review/route";
import { Route as AuthedOnboardedSubscribedLegacyLibraryIndexImport } from "./routes/_authed/_onboarded/_subscribed/_legacy/library/index";
import { Route as AuthedOnboardedSubscribedLegacyForumIndexImport } from "./routes/_authed/_onboarded/_subscribed/_legacy/forum/index";
import { Route as AuthedOnboardedSubscribedLegacyAdminIndexImport } from "./routes/_authed/_onboarded/_subscribed/_legacy/admin/index";
import { Route as AuthedOnboardedSubscribedLegacyLearningPathsLearningPathIdImport } from "./routes/_authed/_onboarded/_subscribed/_legacy/learning-paths/$learningPathId";
import { Route as AuthedOnboardedSubscribedLegacyForumQuestionIdImport } from "./routes/_authed/_onboarded/_subscribed/_legacy/forum/$questionId";
import { Route as AuthedOnboardedSubscribedLegacyDictionaryArticleIdImport } from "./routes/_authed/_onboarded/_subscribed/_legacy/dictionary/$articleId";
import { Route as AuthedOnboardedSubscribedLegacyCasesCaseIdImport } from "./routes/_authed/_onboarded/_subscribed/_legacy/cases/$caseId";
import { Route as AuthedOnboardedFreeFlashcardsTrashBinSetsImport } from "./routes/_authed/_onboarded/_free/flashcards/trash-bin/sets";
import { Route as AuthedOnboardedFreeFlashcardsTrashBinCardsImport } from "./routes/_authed/_onboarded/_free/flashcards/trash-bin/cards";
import { Route as AuthedOnboardedFreeFlashcardsSetsSetIdImport } from "./routes/_authed/_onboarded/_free/flashcards/sets/$setId";
import { Route as AuthedOnboardedFreeFlashcardsReviewAllImport } from "./routes/_authed/_onboarded/_free/flashcards/review/all";
import { Route as AuthedOnboardedFreeFlashcardsReviewSetIdImport } from "./routes/_authed/_onboarded/_free/flashcards/review/$setId";
import { Route as AuthedOnboardedFreeFlashcardsReviewManualAllImport } from "./routes/_authed/_onboarded/_free/flashcards/review/manual/all";
import { Route as AuthedOnboardedFreeFlashcardsReviewManualSetIdImport } from "./routes/_authed/_onboarded/_free/flashcards/review/manual/$setId";

// Create/Update Routes

const RecoverRoute = RecoverImport.update({
  id: "/recover",
  path: "/recover",
  getParentRoute: () => rootRoute,
} as any);

const NotAuthedRouteRoute = NotAuthedRouteImport.update({
  id: "/_not-authed",
  getParentRoute: () => rootRoute,
} as any);

const AuthedRouteRoute = AuthedRouteImport.update({
  id: "/_authed",
  getParentRoute: () => rootRoute,
} as any);

const IndexRoute = IndexImport.update({
  id: "/",
  path: "/",
  getParentRoute: () => rootRoute,
} as any);

const NotAuthedRegisterRoute = NotAuthedRegisterImport.update({
  id: "/register",
  path: "/register",
  getParentRoute: () => NotAuthedRouteRoute,
} as any);

const NotAuthedLoginRoute = NotAuthedLoginImport.update({
  id: "/login",
  path: "/login",
  getParentRoute: () => NotAuthedRouteRoute,
} as any);

const NotAuthedConfirmRoute = NotAuthedConfirmImport.update({
  id: "/confirm",
  path: "/confirm",
  getParentRoute: () => NotAuthedRouteRoute,
} as any);

const AuthedOnboardedRouteRoute = AuthedOnboardedRouteImport.update({
  id: "/_onboarded",
  getParentRoute: () => AuthedRouteRoute,
} as any);

const AuthedOnboardingIndexRoute = AuthedOnboardingIndexImport.update({
  id: "/onboarding/",
  path: "/onboarding/",
  getParentRoute: () => AuthedRouteRoute,
} as any);

const NotAuthedExtensionSelectionRoute =
  NotAuthedExtensionSelectionImport.update({
    id: "/extension/selection",
    path: "/extension/selection",
    getParentRoute: () => NotAuthedRouteRoute,
  } as any);

const NotAuthedExtensionDndRoute = NotAuthedExtensionDndImport.update({
  id: "/extension/dnd",
  path: "/extension/dnd",
  getParentRoute: () => NotAuthedRouteRoute,
} as any);

const AuthedOnboardedSubscribedRouteRoute =
  AuthedOnboardedSubscribedRouteImport.update({
    id: "/_subscribed",
    getParentRoute: () => AuthedOnboardedRouteRoute,
  } as any);

const AuthedOnboardedSubscribedLegacyRouteRoute =
  AuthedOnboardedSubscribedLegacyRouteImport.update({
    id: "/_legacy",
    getParentRoute: () => AuthedOnboardedSubscribedRouteRoute,
  } as any);

const AuthedOnboardedFreeSearchRouteRoute =
  AuthedOnboardedFreeSearchRouteImport.update({
    id: "/_free/search",
    path: "/search",
    getParentRoute: () => AuthedOnboardedRouteRoute,
  } as any);

const AuthedOnboardedFreeFlashcardsRouteRoute =
  AuthedOnboardedFreeFlashcardsRouteImport.update({
    id: "/_free/flashcards",
    path: "/flashcards",
    getParentRoute: () => AuthedOnboardedRouteRoute,
  } as any);

const AuthedOnboardedFreeLegacyRouteRoute =
  AuthedOnboardedFreeLegacyRouteImport.update({
    id: "/_free/_legacy",
    getParentRoute: () => AuthedOnboardedRouteRoute,
  } as any);

const AuthedOnboardedSubscribedLearningPathsIndexRoute =
  AuthedOnboardedSubscribedLearningPathsIndexImport.update({
    id: "/learning-paths/",
    path: "/learning-paths/",
    getParentRoute: () => AuthedOnboardedSubscribedRouteRoute,
  } as any);

const AuthedOnboardedFreeSearchIndexRoute =
  AuthedOnboardedFreeSearchIndexImport.update({
    id: "/",
    path: "/",
    getParentRoute: () => AuthedOnboardedFreeSearchRouteRoute,
  } as any);

const AuthedOnboardedFreeFlashcardsIndexRoute =
  AuthedOnboardedFreeFlashcardsIndexImport.update({
    id: "/",
    path: "/",
    getParentRoute: () => AuthedOnboardedFreeFlashcardsRouteRoute,
  } as any);

const AuthedOnboardedSubscribedLegacyPersonalSpaceRoute =
  AuthedOnboardedSubscribedLegacyPersonalSpaceImport.update({
    id: "/personal-space",
    path: "/personal-space",
    getParentRoute: () => AuthedOnboardedSubscribedLegacyRouteRoute,
  } as any);

const AuthedOnboardedSubscribedLegacyPaymentSuccessRoute =
  AuthedOnboardedSubscribedLegacyPaymentSuccessImport.update({
    id: "/payment-success",
    path: "/payment-success",
    getParentRoute: () => AuthedOnboardedSubscribedLegacyRouteRoute,
  } as any);

const AuthedOnboardedSubscribedLegacyNotificationsRoute =
  AuthedOnboardedSubscribedLegacyNotificationsImport.update({
    id: "/notifications",
    path: "/notifications",
    getParentRoute: () => AuthedOnboardedSubscribedLegacyRouteRoute,
  } as any);

const AuthedOnboardedSubscribedLegacyConfirmEmailChangeRoute =
  AuthedOnboardedSubscribedLegacyConfirmEmailChangeImport.update({
    id: "/confirm-email-change",
    path: "/confirm-email-change",
    getParentRoute: () => AuthedOnboardedSubscribedLegacyRouteRoute,
  } as any);

const AuthedOnboardedFreeFlashcardsMySetsRoute =
  AuthedOnboardedFreeFlashcardsMySetsImport.update({
    id: "/my-sets",
    path: "/my-sets",
    getParentRoute: () => AuthedOnboardedFreeFlashcardsRouteRoute,
  } as any);

const AuthedOnboardedFreeFlashcardsMyCardsRoute =
  AuthedOnboardedFreeFlashcardsMyCardsImport.update({
    id: "/my-cards",
    path: "/my-cards",
    getParentRoute: () => AuthedOnboardedFreeFlashcardsRouteRoute,
  } as any);

const AuthedOnboardedFreeLegacyProfileRoute =
  AuthedOnboardedFreeLegacyProfileImport.update({
    id: "/profile",
    path: "/profile",
    getParentRoute: () => AuthedOnboardedFreeLegacyRouteRoute,
  } as any);

const AuthedOnboardedFreeLegacyDashboardRoute =
  AuthedOnboardedFreeLegacyDashboardImport.update({
    id: "/dashboard",
    path: "/dashboard",
    getParentRoute: () => AuthedOnboardedFreeLegacyRouteRoute,
  } as any);

const AuthedOnboardedFreeFlashcardsTrashBinRouteRoute =
  AuthedOnboardedFreeFlashcardsTrashBinRouteImport.update({
    id: "/trash-bin",
    path: "/trash-bin",
    getParentRoute: () => AuthedOnboardedFreeFlashcardsRouteRoute,
  } as any);

const AuthedOnboardedFreeFlashcardsReviewRouteRoute =
  AuthedOnboardedFreeFlashcardsReviewRouteImport.update({
    id: "/review",
    path: "/review",
    getParentRoute: () => AuthedOnboardedFreeFlashcardsRouteRoute,
  } as any);

const AuthedOnboardedSubscribedLegacyLibraryIndexRoute =
  AuthedOnboardedSubscribedLegacyLibraryIndexImport.update({
    id: "/library/",
    path: "/library/",
    getParentRoute: () => AuthedOnboardedSubscribedLegacyRouteRoute,
  } as any);

const AuthedOnboardedSubscribedLegacyForumIndexRoute =
  AuthedOnboardedSubscribedLegacyForumIndexImport.update({
    id: "/forum/",
    path: "/forum/",
    getParentRoute: () => AuthedOnboardedSubscribedLegacyRouteRoute,
  } as any);

const AuthedOnboardedSubscribedLegacyAdminIndexRoute =
  AuthedOnboardedSubscribedLegacyAdminIndexImport.update({
    id: "/admin/",
    path: "/admin/",
    getParentRoute: () => AuthedOnboardedSubscribedLegacyRouteRoute,
  } as any);

const AuthedOnboardedSubscribedLegacyLearningPathsLearningPathIdRoute =
  AuthedOnboardedSubscribedLegacyLearningPathsLearningPathIdImport.update({
    id: "/learning-paths/$learningPathId",
    path: "/learning-paths/$learningPathId",
    getParentRoute: () => AuthedOnboardedSubscribedLegacyRouteRoute,
  } as any);

const AuthedOnboardedSubscribedLegacyForumQuestionIdRoute =
  AuthedOnboardedSubscribedLegacyForumQuestionIdImport.update({
    id: "/forum/$questionId",
    path: "/forum/$questionId",
    getParentRoute: () => AuthedOnboardedSubscribedLegacyRouteRoute,
  } as any);

const AuthedOnboardedSubscribedLegacyDictionaryArticleIdRoute =
  AuthedOnboardedSubscribedLegacyDictionaryArticleIdImport.update({
    id: "/dictionary/$articleId",
    path: "/dictionary/$articleId",
    getParentRoute: () => AuthedOnboardedSubscribedLegacyRouteRoute,
  } as any);

const AuthedOnboardedSubscribedLegacyCasesCaseIdRoute =
  AuthedOnboardedSubscribedLegacyCasesCaseIdImport.update({
    id: "/cases/$caseId",
    path: "/cases/$caseId",
    getParentRoute: () => AuthedOnboardedSubscribedLegacyRouteRoute,
  } as any);

const AuthedOnboardedFreeFlashcardsTrashBinSetsRoute =
  AuthedOnboardedFreeFlashcardsTrashBinSetsImport.update({
    id: "/sets",
    path: "/sets",
    getParentRoute: () => AuthedOnboardedFreeFlashcardsTrashBinRouteRoute,
  } as any);

const AuthedOnboardedFreeFlashcardsTrashBinCardsRoute =
  AuthedOnboardedFreeFlashcardsTrashBinCardsImport.update({
    id: "/cards",
    path: "/cards",
    getParentRoute: () => AuthedOnboardedFreeFlashcardsTrashBinRouteRoute,
  } as any);

const AuthedOnboardedFreeFlashcardsSetsSetIdRoute =
  AuthedOnboardedFreeFlashcardsSetsSetIdImport.update({
    id: "/sets/$setId",
    path: "/sets/$setId",
    getParentRoute: () => AuthedOnboardedFreeFlashcardsRouteRoute,
  } as any);

const AuthedOnboardedFreeFlashcardsReviewAllRoute =
  AuthedOnboardedFreeFlashcardsReviewAllImport.update({
    id: "/all",
    path: "/all",
    getParentRoute: () => AuthedOnboardedFreeFlashcardsReviewRouteRoute,
  } as any);

const AuthedOnboardedFreeFlashcardsReviewSetIdRoute =
  AuthedOnboardedFreeFlashcardsReviewSetIdImport.update({
    id: "/$setId",
    path: "/$setId",
    getParentRoute: () => AuthedOnboardedFreeFlashcardsReviewRouteRoute,
  } as any);

const AuthedOnboardedFreeFlashcardsReviewManualAllRoute =
  AuthedOnboardedFreeFlashcardsReviewManualAllImport.update({
    id: "/manual/all",
    path: "/manual/all",
    getParentRoute: () => AuthedOnboardedFreeFlashcardsReviewRouteRoute,
  } as any);

const AuthedOnboardedFreeFlashcardsReviewManualSetIdRoute =
  AuthedOnboardedFreeFlashcardsReviewManualSetIdImport.update({
    id: "/manual/$setId",
    path: "/manual/$setId",
    getParentRoute: () => AuthedOnboardedFreeFlashcardsReviewRouteRoute,
  } as any);

// Populate the FileRoutesByPath interface

declare module "@tanstack/react-router" {
  interface FileRoutesByPath {
    "/": {
      id: "/";
      path: "/";
      fullPath: "/";
      preLoaderRoute: typeof IndexImport;
      parentRoute: typeof rootRoute;
    };
    "/_authed": {
      id: "/_authed";
      path: "";
      fullPath: "";
      preLoaderRoute: typeof AuthedRouteImport;
      parentRoute: typeof rootRoute;
    };
    "/_not-authed": {
      id: "/_not-authed";
      path: "";
      fullPath: "";
      preLoaderRoute: typeof NotAuthedRouteImport;
      parentRoute: typeof rootRoute;
    };
    "/recover": {
      id: "/recover";
      path: "/recover";
      fullPath: "/recover";
      preLoaderRoute: typeof RecoverImport;
      parentRoute: typeof rootRoute;
    };
    "/_authed/_onboarded": {
      id: "/_authed/_onboarded";
      path: "";
      fullPath: "";
      preLoaderRoute: typeof AuthedOnboardedRouteImport;
      parentRoute: typeof AuthedRouteImport;
    };
    "/_not-authed/confirm": {
      id: "/_not-authed/confirm";
      path: "/confirm";
      fullPath: "/confirm";
      preLoaderRoute: typeof NotAuthedConfirmImport;
      parentRoute: typeof NotAuthedRouteImport;
    };
    "/_not-authed/login": {
      id: "/_not-authed/login";
      path: "/login";
      fullPath: "/login";
      preLoaderRoute: typeof NotAuthedLoginImport;
      parentRoute: typeof NotAuthedRouteImport;
    };
    "/_not-authed/register": {
      id: "/_not-authed/register";
      path: "/register";
      fullPath: "/register";
      preLoaderRoute: typeof NotAuthedRegisterImport;
      parentRoute: typeof NotAuthedRouteImport;
    };
    "/_authed/_onboarded/_subscribed": {
      id: "/_authed/_onboarded/_subscribed";
      path: "";
      fullPath: "";
      preLoaderRoute: typeof AuthedOnboardedSubscribedRouteImport;
      parentRoute: typeof AuthedOnboardedRouteImport;
    };
    "/_not-authed/extension/dnd": {
      id: "/_not-authed/extension/dnd";
      path: "/extension/dnd";
      fullPath: "/extension/dnd";
      preLoaderRoute: typeof NotAuthedExtensionDndImport;
      parentRoute: typeof NotAuthedRouteImport;
    };
    "/_not-authed/extension/selection": {
      id: "/_not-authed/extension/selection";
      path: "/extension/selection";
      fullPath: "/extension/selection";
      preLoaderRoute: typeof NotAuthedExtensionSelectionImport;
      parentRoute: typeof NotAuthedRouteImport;
    };
    "/_authed/onboarding/": {
      id: "/_authed/onboarding/";
      path: "/onboarding";
      fullPath: "/onboarding";
      preLoaderRoute: typeof AuthedOnboardingIndexImport;
      parentRoute: typeof AuthedRouteImport;
    };
    "/_authed/_onboarded/_free/_legacy": {
      id: "/_authed/_onboarded/_free/_legacy";
      path: "";
      fullPath: "";
      preLoaderRoute: typeof AuthedOnboardedFreeLegacyRouteImport;
      parentRoute: typeof AuthedOnboardedRouteImport;
    };
    "/_authed/_onboarded/_free/flashcards": {
      id: "/_authed/_onboarded/_free/flashcards";
      path: "/flashcards";
      fullPath: "/flashcards";
      preLoaderRoute: typeof AuthedOnboardedFreeFlashcardsRouteImport;
      parentRoute: typeof AuthedOnboardedRouteImport;
    };
    "/_authed/_onboarded/_free/search": {
      id: "/_authed/_onboarded/_free/search";
      path: "/search";
      fullPath: "/search";
      preLoaderRoute: typeof AuthedOnboardedFreeSearchRouteImport;
      parentRoute: typeof AuthedOnboardedRouteImport;
    };
    "/_authed/_onboarded/_subscribed/_legacy": {
      id: "/_authed/_onboarded/_subscribed/_legacy";
      path: "";
      fullPath: "";
      preLoaderRoute: typeof AuthedOnboardedSubscribedLegacyRouteImport;
      parentRoute: typeof AuthedOnboardedSubscribedRouteImport;
    };
    "/_authed/_onboarded/_free/flashcards/review": {
      id: "/_authed/_onboarded/_free/flashcards/review";
      path: "/review";
      fullPath: "/flashcards/review";
      preLoaderRoute: typeof AuthedOnboardedFreeFlashcardsReviewRouteImport;
      parentRoute: typeof AuthedOnboardedFreeFlashcardsRouteImport;
    };
    "/_authed/_onboarded/_free/flashcards/trash-bin": {
      id: "/_authed/_onboarded/_free/flashcards/trash-bin";
      path: "/trash-bin";
      fullPath: "/flashcards/trash-bin";
      preLoaderRoute: typeof AuthedOnboardedFreeFlashcardsTrashBinRouteImport;
      parentRoute: typeof AuthedOnboardedFreeFlashcardsRouteImport;
    };
    "/_authed/_onboarded/_free/_legacy/dashboard": {
      id: "/_authed/_onboarded/_free/_legacy/dashboard";
      path: "/dashboard";
      fullPath: "/dashboard";
      preLoaderRoute: typeof AuthedOnboardedFreeLegacyDashboardImport;
      parentRoute: typeof AuthedOnboardedFreeLegacyRouteImport;
    };
    "/_authed/_onboarded/_free/_legacy/profile": {
      id: "/_authed/_onboarded/_free/_legacy/profile";
      path: "/profile";
      fullPath: "/profile";
      preLoaderRoute: typeof AuthedOnboardedFreeLegacyProfileImport;
      parentRoute: typeof AuthedOnboardedFreeLegacyRouteImport;
    };
    "/_authed/_onboarded/_free/flashcards/my-cards": {
      id: "/_authed/_onboarded/_free/flashcards/my-cards";
      path: "/my-cards";
      fullPath: "/flashcards/my-cards";
      preLoaderRoute: typeof AuthedOnboardedFreeFlashcardsMyCardsImport;
      parentRoute: typeof AuthedOnboardedFreeFlashcardsRouteImport;
    };
    "/_authed/_onboarded/_free/flashcards/my-sets": {
      id: "/_authed/_onboarded/_free/flashcards/my-sets";
      path: "/my-sets";
      fullPath: "/flashcards/my-sets";
      preLoaderRoute: typeof AuthedOnboardedFreeFlashcardsMySetsImport;
      parentRoute: typeof AuthedOnboardedFreeFlashcardsRouteImport;
    };
    "/_authed/_onboarded/_subscribed/_legacy/confirm-email-change": {
      id: "/_authed/_onboarded/_subscribed/_legacy/confirm-email-change";
      path: "/confirm-email-change";
      fullPath: "/confirm-email-change";
      preLoaderRoute: typeof AuthedOnboardedSubscribedLegacyConfirmEmailChangeImport;
      parentRoute: typeof AuthedOnboardedSubscribedLegacyRouteImport;
    };
    "/_authed/_onboarded/_subscribed/_legacy/notifications": {
      id: "/_authed/_onboarded/_subscribed/_legacy/notifications";
      path: "/notifications";
      fullPath: "/notifications";
      preLoaderRoute: typeof AuthedOnboardedSubscribedLegacyNotificationsImport;
      parentRoute: typeof AuthedOnboardedSubscribedLegacyRouteImport;
    };
    "/_authed/_onboarded/_subscribed/_legacy/payment-success": {
      id: "/_authed/_onboarded/_subscribed/_legacy/payment-success";
      path: "/payment-success";
      fullPath: "/payment-success";
      preLoaderRoute: typeof AuthedOnboardedSubscribedLegacyPaymentSuccessImport;
      parentRoute: typeof AuthedOnboardedSubscribedLegacyRouteImport;
    };
    "/_authed/_onboarded/_subscribed/_legacy/personal-space": {
      id: "/_authed/_onboarded/_subscribed/_legacy/personal-space";
      path: "/personal-space";
      fullPath: "/personal-space";
      preLoaderRoute: typeof AuthedOnboardedSubscribedLegacyPersonalSpaceImport;
      parentRoute: typeof AuthedOnboardedSubscribedLegacyRouteImport;
    };
    "/_authed/_onboarded/_free/flashcards/": {
      id: "/_authed/_onboarded/_free/flashcards/";
      path: "/";
      fullPath: "/flashcards/";
      preLoaderRoute: typeof AuthedOnboardedFreeFlashcardsIndexImport;
      parentRoute: typeof AuthedOnboardedFreeFlashcardsRouteImport;
    };
    "/_authed/_onboarded/_free/search/": {
      id: "/_authed/_onboarded/_free/search/";
      path: "/";
      fullPath: "/search/";
      preLoaderRoute: typeof AuthedOnboardedFreeSearchIndexImport;
      parentRoute: typeof AuthedOnboardedFreeSearchRouteImport;
    };
    "/_authed/_onboarded/_subscribed/learning-paths/": {
      id: "/_authed/_onboarded/_subscribed/learning-paths/";
      path: "/learning-paths";
      fullPath: "/learning-paths";
      preLoaderRoute: typeof AuthedOnboardedSubscribedLearningPathsIndexImport;
      parentRoute: typeof AuthedOnboardedSubscribedRouteImport;
    };
    "/_authed/_onboarded/_free/flashcards/review/$setId": {
      id: "/_authed/_onboarded/_free/flashcards/review/$setId";
      path: "/$setId";
      fullPath: "/flashcards/review/$setId";
      preLoaderRoute: typeof AuthedOnboardedFreeFlashcardsReviewSetIdImport;
      parentRoute: typeof AuthedOnboardedFreeFlashcardsReviewRouteImport;
    };
    "/_authed/_onboarded/_free/flashcards/review/all": {
      id: "/_authed/_onboarded/_free/flashcards/review/all";
      path: "/all";
      fullPath: "/flashcards/review/all";
      preLoaderRoute: typeof AuthedOnboardedFreeFlashcardsReviewAllImport;
      parentRoute: typeof AuthedOnboardedFreeFlashcardsReviewRouteImport;
    };
    "/_authed/_onboarded/_free/flashcards/sets/$setId": {
      id: "/_authed/_onboarded/_free/flashcards/sets/$setId";
      path: "/sets/$setId";
      fullPath: "/flashcards/sets/$setId";
      preLoaderRoute: typeof AuthedOnboardedFreeFlashcardsSetsSetIdImport;
      parentRoute: typeof AuthedOnboardedFreeFlashcardsRouteImport;
    };
    "/_authed/_onboarded/_free/flashcards/trash-bin/cards": {
      id: "/_authed/_onboarded/_free/flashcards/trash-bin/cards";
      path: "/cards";
      fullPath: "/flashcards/trash-bin/cards";
      preLoaderRoute: typeof AuthedOnboardedFreeFlashcardsTrashBinCardsImport;
      parentRoute: typeof AuthedOnboardedFreeFlashcardsTrashBinRouteImport;
    };
    "/_authed/_onboarded/_free/flashcards/trash-bin/sets": {
      id: "/_authed/_onboarded/_free/flashcards/trash-bin/sets";
      path: "/sets";
      fullPath: "/flashcards/trash-bin/sets";
      preLoaderRoute: typeof AuthedOnboardedFreeFlashcardsTrashBinSetsImport;
      parentRoute: typeof AuthedOnboardedFreeFlashcardsTrashBinRouteImport;
    };
    "/_authed/_onboarded/_subscribed/_legacy/cases/$caseId": {
      id: "/_authed/_onboarded/_subscribed/_legacy/cases/$caseId";
      path: "/cases/$caseId";
      fullPath: "/cases/$caseId";
      preLoaderRoute: typeof AuthedOnboardedSubscribedLegacyCasesCaseIdImport;
      parentRoute: typeof AuthedOnboardedSubscribedLegacyRouteImport;
    };
    "/_authed/_onboarded/_subscribed/_legacy/dictionary/$articleId": {
      id: "/_authed/_onboarded/_subscribed/_legacy/dictionary/$articleId";
      path: "/dictionary/$articleId";
      fullPath: "/dictionary/$articleId";
      preLoaderRoute: typeof AuthedOnboardedSubscribedLegacyDictionaryArticleIdImport;
      parentRoute: typeof AuthedOnboardedSubscribedLegacyRouteImport;
    };
    "/_authed/_onboarded/_subscribed/_legacy/forum/$questionId": {
      id: "/_authed/_onboarded/_subscribed/_legacy/forum/$questionId";
      path: "/forum/$questionId";
      fullPath: "/forum/$questionId";
      preLoaderRoute: typeof AuthedOnboardedSubscribedLegacyForumQuestionIdImport;
      parentRoute: typeof AuthedOnboardedSubscribedLegacyRouteImport;
    };
    "/_authed/_onboarded/_subscribed/_legacy/learning-paths/$learningPathId": {
      id: "/_authed/_onboarded/_subscribed/_legacy/learning-paths/$learningPathId";
      path: "/learning-paths/$learningPathId";
      fullPath: "/learning-paths/$learningPathId";
      preLoaderRoute: typeof AuthedOnboardedSubscribedLegacyLearningPathsLearningPathIdImport;
      parentRoute: typeof AuthedOnboardedSubscribedLegacyRouteImport;
    };
    "/_authed/_onboarded/_subscribed/_legacy/admin/": {
      id: "/_authed/_onboarded/_subscribed/_legacy/admin/";
      path: "/admin";
      fullPath: "/admin";
      preLoaderRoute: typeof AuthedOnboardedSubscribedLegacyAdminIndexImport;
      parentRoute: typeof AuthedOnboardedSubscribedLegacyRouteImport;
    };
    "/_authed/_onboarded/_subscribed/_legacy/forum/": {
      id: "/_authed/_onboarded/_subscribed/_legacy/forum/";
      path: "/forum";
      fullPath: "/forum";
      preLoaderRoute: typeof AuthedOnboardedSubscribedLegacyForumIndexImport;
      parentRoute: typeof AuthedOnboardedSubscribedLegacyRouteImport;
    };
    "/_authed/_onboarded/_subscribed/_legacy/library/": {
      id: "/_authed/_onboarded/_subscribed/_legacy/library/";
      path: "/library";
      fullPath: "/library";
      preLoaderRoute: typeof AuthedOnboardedSubscribedLegacyLibraryIndexImport;
      parentRoute: typeof AuthedOnboardedSubscribedLegacyRouteImport;
    };
    "/_authed/_onboarded/_free/flashcards/review/manual/$setId": {
      id: "/_authed/_onboarded/_free/flashcards/review/manual/$setId";
      path: "/manual/$setId";
      fullPath: "/flashcards/review/manual/$setId";
      preLoaderRoute: typeof AuthedOnboardedFreeFlashcardsReviewManualSetIdImport;
      parentRoute: typeof AuthedOnboardedFreeFlashcardsReviewRouteImport;
    };
    "/_authed/_onboarded/_free/flashcards/review/manual/all": {
      id: "/_authed/_onboarded/_free/flashcards/review/manual/all";
      path: "/manual/all";
      fullPath: "/flashcards/review/manual/all";
      preLoaderRoute: typeof AuthedOnboardedFreeFlashcardsReviewManualAllImport;
      parentRoute: typeof AuthedOnboardedFreeFlashcardsReviewRouteImport;
    };
  }
}

// Create and export the route tree

interface AuthedOnboardedSubscribedLegacyRouteRouteChildren {
  AuthedOnboardedSubscribedLegacyConfirmEmailChangeRoute: typeof AuthedOnboardedSubscribedLegacyConfirmEmailChangeRoute;
  AuthedOnboardedSubscribedLegacyNotificationsRoute: typeof AuthedOnboardedSubscribedLegacyNotificationsRoute;
  AuthedOnboardedSubscribedLegacyPaymentSuccessRoute: typeof AuthedOnboardedSubscribedLegacyPaymentSuccessRoute;
  AuthedOnboardedSubscribedLegacyPersonalSpaceRoute: typeof AuthedOnboardedSubscribedLegacyPersonalSpaceRoute;
  AuthedOnboardedSubscribedLegacyCasesCaseIdRoute: typeof AuthedOnboardedSubscribedLegacyCasesCaseIdRoute;
  AuthedOnboardedSubscribedLegacyDictionaryArticleIdRoute: typeof AuthedOnboardedSubscribedLegacyDictionaryArticleIdRoute;
  AuthedOnboardedSubscribedLegacyForumQuestionIdRoute: typeof AuthedOnboardedSubscribedLegacyForumQuestionIdRoute;
  AuthedOnboardedSubscribedLegacyLearningPathsLearningPathIdRoute: typeof AuthedOnboardedSubscribedLegacyLearningPathsLearningPathIdRoute;
  AuthedOnboardedSubscribedLegacyAdminIndexRoute: typeof AuthedOnboardedSubscribedLegacyAdminIndexRoute;
  AuthedOnboardedSubscribedLegacyForumIndexRoute: typeof AuthedOnboardedSubscribedLegacyForumIndexRoute;
  AuthedOnboardedSubscribedLegacyLibraryIndexRoute: typeof AuthedOnboardedSubscribedLegacyLibraryIndexRoute;
}

const AuthedOnboardedSubscribedLegacyRouteRouteChildren: AuthedOnboardedSubscribedLegacyRouteRouteChildren =
  {
    AuthedOnboardedSubscribedLegacyConfirmEmailChangeRoute:
      AuthedOnboardedSubscribedLegacyConfirmEmailChangeRoute,
    AuthedOnboardedSubscribedLegacyNotificationsRoute:
      AuthedOnboardedSubscribedLegacyNotificationsRoute,
    AuthedOnboardedSubscribedLegacyPaymentSuccessRoute:
      AuthedOnboardedSubscribedLegacyPaymentSuccessRoute,
    AuthedOnboardedSubscribedLegacyPersonalSpaceRoute:
      AuthedOnboardedSubscribedLegacyPersonalSpaceRoute,
    AuthedOnboardedSubscribedLegacyCasesCaseIdRoute:
      AuthedOnboardedSubscribedLegacyCasesCaseIdRoute,
    AuthedOnboardedSubscribedLegacyDictionaryArticleIdRoute:
      AuthedOnboardedSubscribedLegacyDictionaryArticleIdRoute,
    AuthedOnboardedSubscribedLegacyForumQuestionIdRoute:
      AuthedOnboardedSubscribedLegacyForumQuestionIdRoute,
    AuthedOnboardedSubscribedLegacyLearningPathsLearningPathIdRoute:
      AuthedOnboardedSubscribedLegacyLearningPathsLearningPathIdRoute,
    AuthedOnboardedSubscribedLegacyAdminIndexRoute:
      AuthedOnboardedSubscribedLegacyAdminIndexRoute,
    AuthedOnboardedSubscribedLegacyForumIndexRoute:
      AuthedOnboardedSubscribedLegacyForumIndexRoute,
    AuthedOnboardedSubscribedLegacyLibraryIndexRoute:
      AuthedOnboardedSubscribedLegacyLibraryIndexRoute,
  };

const AuthedOnboardedSubscribedLegacyRouteRouteWithChildren =
  AuthedOnboardedSubscribedLegacyRouteRoute._addFileChildren(
    AuthedOnboardedSubscribedLegacyRouteRouteChildren,
  );

interface AuthedOnboardedSubscribedRouteRouteChildren {
  AuthedOnboardedSubscribedLegacyRouteRoute: typeof AuthedOnboardedSubscribedLegacyRouteRouteWithChildren;
  AuthedOnboardedSubscribedLearningPathsIndexRoute: typeof AuthedOnboardedSubscribedLearningPathsIndexRoute;
}

const AuthedOnboardedSubscribedRouteRouteChildren: AuthedOnboardedSubscribedRouteRouteChildren =
  {
    AuthedOnboardedSubscribedLegacyRouteRoute:
      AuthedOnboardedSubscribedLegacyRouteRouteWithChildren,
    AuthedOnboardedSubscribedLearningPathsIndexRoute:
      AuthedOnboardedSubscribedLearningPathsIndexRoute,
  };

const AuthedOnboardedSubscribedRouteRouteWithChildren =
  AuthedOnboardedSubscribedRouteRoute._addFileChildren(
    AuthedOnboardedSubscribedRouteRouteChildren,
  );

interface AuthedOnboardedFreeLegacyRouteRouteChildren {
  AuthedOnboardedFreeLegacyDashboardRoute: typeof AuthedOnboardedFreeLegacyDashboardRoute;
  AuthedOnboardedFreeLegacyProfileRoute: typeof AuthedOnboardedFreeLegacyProfileRoute;
}

const AuthedOnboardedFreeLegacyRouteRouteChildren: AuthedOnboardedFreeLegacyRouteRouteChildren =
  {
    AuthedOnboardedFreeLegacyDashboardRoute:
      AuthedOnboardedFreeLegacyDashboardRoute,
    AuthedOnboardedFreeLegacyProfileRoute:
      AuthedOnboardedFreeLegacyProfileRoute,
  };

const AuthedOnboardedFreeLegacyRouteRouteWithChildren =
  AuthedOnboardedFreeLegacyRouteRoute._addFileChildren(
    AuthedOnboardedFreeLegacyRouteRouteChildren,
  );

interface AuthedOnboardedFreeFlashcardsReviewRouteRouteChildren {
  AuthedOnboardedFreeFlashcardsReviewSetIdRoute: typeof AuthedOnboardedFreeFlashcardsReviewSetIdRoute;
  AuthedOnboardedFreeFlashcardsReviewAllRoute: typeof AuthedOnboardedFreeFlashcardsReviewAllRoute;
  AuthedOnboardedFreeFlashcardsReviewManualSetIdRoute: typeof AuthedOnboardedFreeFlashcardsReviewManualSetIdRoute;
  AuthedOnboardedFreeFlashcardsReviewManualAllRoute: typeof AuthedOnboardedFreeFlashcardsReviewManualAllRoute;
}

const AuthedOnboardedFreeFlashcardsReviewRouteRouteChildren: AuthedOnboardedFreeFlashcardsReviewRouteRouteChildren =
  {
    AuthedOnboardedFreeFlashcardsReviewSetIdRoute:
      AuthedOnboardedFreeFlashcardsReviewSetIdRoute,
    AuthedOnboardedFreeFlashcardsReviewAllRoute:
      AuthedOnboardedFreeFlashcardsReviewAllRoute,
    AuthedOnboardedFreeFlashcardsReviewManualSetIdRoute:
      AuthedOnboardedFreeFlashcardsReviewManualSetIdRoute,
    AuthedOnboardedFreeFlashcardsReviewManualAllRoute:
      AuthedOnboardedFreeFlashcardsReviewManualAllRoute,
  };

const AuthedOnboardedFreeFlashcardsReviewRouteRouteWithChildren =
  AuthedOnboardedFreeFlashcardsReviewRouteRoute._addFileChildren(
    AuthedOnboardedFreeFlashcardsReviewRouteRouteChildren,
  );

interface AuthedOnboardedFreeFlashcardsTrashBinRouteRouteChildren {
  AuthedOnboardedFreeFlashcardsTrashBinCardsRoute: typeof AuthedOnboardedFreeFlashcardsTrashBinCardsRoute;
  AuthedOnboardedFreeFlashcardsTrashBinSetsRoute: typeof AuthedOnboardedFreeFlashcardsTrashBinSetsRoute;
}

const AuthedOnboardedFreeFlashcardsTrashBinRouteRouteChildren: AuthedOnboardedFreeFlashcardsTrashBinRouteRouteChildren =
  {
    AuthedOnboardedFreeFlashcardsTrashBinCardsRoute:
      AuthedOnboardedFreeFlashcardsTrashBinCardsRoute,
    AuthedOnboardedFreeFlashcardsTrashBinSetsRoute:
      AuthedOnboardedFreeFlashcardsTrashBinSetsRoute,
  };

const AuthedOnboardedFreeFlashcardsTrashBinRouteRouteWithChildren =
  AuthedOnboardedFreeFlashcardsTrashBinRouteRoute._addFileChildren(
    AuthedOnboardedFreeFlashcardsTrashBinRouteRouteChildren,
  );

interface AuthedOnboardedFreeFlashcardsRouteRouteChildren {
  AuthedOnboardedFreeFlashcardsReviewRouteRoute: typeof AuthedOnboardedFreeFlashcardsReviewRouteRouteWithChildren;
  AuthedOnboardedFreeFlashcardsTrashBinRouteRoute: typeof AuthedOnboardedFreeFlashcardsTrashBinRouteRouteWithChildren;
  AuthedOnboardedFreeFlashcardsMyCardsRoute: typeof AuthedOnboardedFreeFlashcardsMyCardsRoute;
  AuthedOnboardedFreeFlashcardsMySetsRoute: typeof AuthedOnboardedFreeFlashcardsMySetsRoute;
  AuthedOnboardedFreeFlashcardsIndexRoute: typeof AuthedOnboardedFreeFlashcardsIndexRoute;
  AuthedOnboardedFreeFlashcardsSetsSetIdRoute: typeof AuthedOnboardedFreeFlashcardsSetsSetIdRoute;
}

const AuthedOnboardedFreeFlashcardsRouteRouteChildren: AuthedOnboardedFreeFlashcardsRouteRouteChildren =
  {
    AuthedOnboardedFreeFlashcardsReviewRouteRoute:
      AuthedOnboardedFreeFlashcardsReviewRouteRouteWithChildren,
    AuthedOnboardedFreeFlashcardsTrashBinRouteRoute:
      AuthedOnboardedFreeFlashcardsTrashBinRouteRouteWithChildren,
    AuthedOnboardedFreeFlashcardsMyCardsRoute:
      AuthedOnboardedFreeFlashcardsMyCardsRoute,
    AuthedOnboardedFreeFlashcardsMySetsRoute:
      AuthedOnboardedFreeFlashcardsMySetsRoute,
    AuthedOnboardedFreeFlashcardsIndexRoute:
      AuthedOnboardedFreeFlashcardsIndexRoute,
    AuthedOnboardedFreeFlashcardsSetsSetIdRoute:
      AuthedOnboardedFreeFlashcardsSetsSetIdRoute,
  };

const AuthedOnboardedFreeFlashcardsRouteRouteWithChildren =
  AuthedOnboardedFreeFlashcardsRouteRoute._addFileChildren(
    AuthedOnboardedFreeFlashcardsRouteRouteChildren,
  );

interface AuthedOnboardedFreeSearchRouteRouteChildren {
  AuthedOnboardedFreeSearchIndexRoute: typeof AuthedOnboardedFreeSearchIndexRoute;
}

const AuthedOnboardedFreeSearchRouteRouteChildren: AuthedOnboardedFreeSearchRouteRouteChildren =
  {
    AuthedOnboardedFreeSearchIndexRoute: AuthedOnboardedFreeSearchIndexRoute,
  };

const AuthedOnboardedFreeSearchRouteRouteWithChildren =
  AuthedOnboardedFreeSearchRouteRoute._addFileChildren(
    AuthedOnboardedFreeSearchRouteRouteChildren,
  );

interface AuthedOnboardedRouteRouteChildren {
  AuthedOnboardedSubscribedRouteRoute: typeof AuthedOnboardedSubscribedRouteRouteWithChildren;
  AuthedOnboardedFreeLegacyRouteRoute: typeof AuthedOnboardedFreeLegacyRouteRouteWithChildren;
  AuthedOnboardedFreeFlashcardsRouteRoute: typeof AuthedOnboardedFreeFlashcardsRouteRouteWithChildren;
  AuthedOnboardedFreeSearchRouteRoute: typeof AuthedOnboardedFreeSearchRouteRouteWithChildren;
}

const AuthedOnboardedRouteRouteChildren: AuthedOnboardedRouteRouteChildren = {
  AuthedOnboardedSubscribedRouteRoute:
    AuthedOnboardedSubscribedRouteRouteWithChildren,
  AuthedOnboardedFreeLegacyRouteRoute:
    AuthedOnboardedFreeLegacyRouteRouteWithChildren,
  AuthedOnboardedFreeFlashcardsRouteRoute:
    AuthedOnboardedFreeFlashcardsRouteRouteWithChildren,
  AuthedOnboardedFreeSearchRouteRoute:
    AuthedOnboardedFreeSearchRouteRouteWithChildren,
};

const AuthedOnboardedRouteRouteWithChildren =
  AuthedOnboardedRouteRoute._addFileChildren(AuthedOnboardedRouteRouteChildren);

interface AuthedRouteRouteChildren {
  AuthedOnboardedRouteRoute: typeof AuthedOnboardedRouteRouteWithChildren;
  AuthedOnboardingIndexRoute: typeof AuthedOnboardingIndexRoute;
}

const AuthedRouteRouteChildren: AuthedRouteRouteChildren = {
  AuthedOnboardedRouteRoute: AuthedOnboardedRouteRouteWithChildren,
  AuthedOnboardingIndexRoute: AuthedOnboardingIndexRoute,
};

const AuthedRouteRouteWithChildren = AuthedRouteRoute._addFileChildren(
  AuthedRouteRouteChildren,
);

interface NotAuthedRouteRouteChildren {
  NotAuthedConfirmRoute: typeof NotAuthedConfirmRoute;
  NotAuthedLoginRoute: typeof NotAuthedLoginRoute;
  NotAuthedRegisterRoute: typeof NotAuthedRegisterRoute;
  NotAuthedExtensionDndRoute: typeof NotAuthedExtensionDndRoute;
  NotAuthedExtensionSelectionRoute: typeof NotAuthedExtensionSelectionRoute;
}

const NotAuthedRouteRouteChildren: NotAuthedRouteRouteChildren = {
  NotAuthedConfirmRoute: NotAuthedConfirmRoute,
  NotAuthedLoginRoute: NotAuthedLoginRoute,
  NotAuthedRegisterRoute: NotAuthedRegisterRoute,
  NotAuthedExtensionDndRoute: NotAuthedExtensionDndRoute,
  NotAuthedExtensionSelectionRoute: NotAuthedExtensionSelectionRoute,
};

const NotAuthedRouteRouteWithChildren = NotAuthedRouteRoute._addFileChildren(
  NotAuthedRouteRouteChildren,
);

export interface FileRoutesByFullPath {
  "/": typeof IndexRoute;
  "": typeof AuthedOnboardedSubscribedLegacyRouteRouteWithChildren;
  "/recover": typeof RecoverRoute;
  "/confirm": typeof NotAuthedConfirmRoute;
  "/login": typeof NotAuthedLoginRoute;
  "/register": typeof NotAuthedRegisterRoute;
  "/extension/dnd": typeof NotAuthedExtensionDndRoute;
  "/extension/selection": typeof NotAuthedExtensionSelectionRoute;
  "/onboarding": typeof AuthedOnboardingIndexRoute;
  "/flashcards": typeof AuthedOnboardedFreeFlashcardsRouteRouteWithChildren;
  "/search": typeof AuthedOnboardedFreeSearchRouteRouteWithChildren;
  "/flashcards/review": typeof AuthedOnboardedFreeFlashcardsReviewRouteRouteWithChildren;
  "/flashcards/trash-bin": typeof AuthedOnboardedFreeFlashcardsTrashBinRouteRouteWithChildren;
  "/dashboard": typeof AuthedOnboardedFreeLegacyDashboardRoute;
  "/profile": typeof AuthedOnboardedFreeLegacyProfileRoute;
  "/flashcards/my-cards": typeof AuthedOnboardedFreeFlashcardsMyCardsRoute;
  "/flashcards/my-sets": typeof AuthedOnboardedFreeFlashcardsMySetsRoute;
  "/confirm-email-change": typeof AuthedOnboardedSubscribedLegacyConfirmEmailChangeRoute;
  "/notifications": typeof AuthedOnboardedSubscribedLegacyNotificationsRoute;
  "/payment-success": typeof AuthedOnboardedSubscribedLegacyPaymentSuccessRoute;
  "/personal-space": typeof AuthedOnboardedSubscribedLegacyPersonalSpaceRoute;
  "/flashcards/": typeof AuthedOnboardedFreeFlashcardsIndexRoute;
  "/search/": typeof AuthedOnboardedFreeSearchIndexRoute;
  "/learning-paths": typeof AuthedOnboardedSubscribedLearningPathsIndexRoute;
  "/flashcards/review/$setId": typeof AuthedOnboardedFreeFlashcardsReviewSetIdRoute;
  "/flashcards/review/all": typeof AuthedOnboardedFreeFlashcardsReviewAllRoute;
  "/flashcards/sets/$setId": typeof AuthedOnboardedFreeFlashcardsSetsSetIdRoute;
  "/flashcards/trash-bin/cards": typeof AuthedOnboardedFreeFlashcardsTrashBinCardsRoute;
  "/flashcards/trash-bin/sets": typeof AuthedOnboardedFreeFlashcardsTrashBinSetsRoute;
  "/cases/$caseId": typeof AuthedOnboardedSubscribedLegacyCasesCaseIdRoute;
  "/dictionary/$articleId": typeof AuthedOnboardedSubscribedLegacyDictionaryArticleIdRoute;
  "/forum/$questionId": typeof AuthedOnboardedSubscribedLegacyForumQuestionIdRoute;
  "/learning-paths/$learningPathId": typeof AuthedOnboardedSubscribedLegacyLearningPathsLearningPathIdRoute;
  "/admin": typeof AuthedOnboardedSubscribedLegacyAdminIndexRoute;
  "/forum": typeof AuthedOnboardedSubscribedLegacyForumIndexRoute;
  "/library": typeof AuthedOnboardedSubscribedLegacyLibraryIndexRoute;
  "/flashcards/review/manual/$setId": typeof AuthedOnboardedFreeFlashcardsReviewManualSetIdRoute;
  "/flashcards/review/manual/all": typeof AuthedOnboardedFreeFlashcardsReviewManualAllRoute;
}

export interface FileRoutesByTo {
  "/": typeof IndexRoute;
  "": typeof AuthedOnboardedSubscribedLegacyRouteRouteWithChildren;
  "/recover": typeof RecoverRoute;
  "/confirm": typeof NotAuthedConfirmRoute;
  "/login": typeof NotAuthedLoginRoute;
  "/register": typeof NotAuthedRegisterRoute;
  "/extension/dnd": typeof NotAuthedExtensionDndRoute;
  "/extension/selection": typeof NotAuthedExtensionSelectionRoute;
  "/onboarding": typeof AuthedOnboardingIndexRoute;
  "/flashcards/review": typeof AuthedOnboardedFreeFlashcardsReviewRouteRouteWithChildren;
  "/flashcards/trash-bin": typeof AuthedOnboardedFreeFlashcardsTrashBinRouteRouteWithChildren;
  "/dashboard": typeof AuthedOnboardedFreeLegacyDashboardRoute;
  "/profile": typeof AuthedOnboardedFreeLegacyProfileRoute;
  "/flashcards/my-cards": typeof AuthedOnboardedFreeFlashcardsMyCardsRoute;
  "/flashcards/my-sets": typeof AuthedOnboardedFreeFlashcardsMySetsRoute;
  "/confirm-email-change": typeof AuthedOnboardedSubscribedLegacyConfirmEmailChangeRoute;
  "/notifications": typeof AuthedOnboardedSubscribedLegacyNotificationsRoute;
  "/payment-success": typeof AuthedOnboardedSubscribedLegacyPaymentSuccessRoute;
  "/personal-space": typeof AuthedOnboardedSubscribedLegacyPersonalSpaceRoute;
  "/flashcards": typeof AuthedOnboardedFreeFlashcardsIndexRoute;
  "/search": typeof AuthedOnboardedFreeSearchIndexRoute;
  "/learning-paths": typeof AuthedOnboardedSubscribedLearningPathsIndexRoute;
  "/flashcards/review/$setId": typeof AuthedOnboardedFreeFlashcardsReviewSetIdRoute;
  "/flashcards/review/all": typeof AuthedOnboardedFreeFlashcardsReviewAllRoute;
  "/flashcards/sets/$setId": typeof AuthedOnboardedFreeFlashcardsSetsSetIdRoute;
  "/flashcards/trash-bin/cards": typeof AuthedOnboardedFreeFlashcardsTrashBinCardsRoute;
  "/flashcards/trash-bin/sets": typeof AuthedOnboardedFreeFlashcardsTrashBinSetsRoute;
  "/cases/$caseId": typeof AuthedOnboardedSubscribedLegacyCasesCaseIdRoute;
  "/dictionary/$articleId": typeof AuthedOnboardedSubscribedLegacyDictionaryArticleIdRoute;
  "/forum/$questionId": typeof AuthedOnboardedSubscribedLegacyForumQuestionIdRoute;
  "/learning-paths/$learningPathId": typeof AuthedOnboardedSubscribedLegacyLearningPathsLearningPathIdRoute;
  "/admin": typeof AuthedOnboardedSubscribedLegacyAdminIndexRoute;
  "/forum": typeof AuthedOnboardedSubscribedLegacyForumIndexRoute;
  "/library": typeof AuthedOnboardedSubscribedLegacyLibraryIndexRoute;
  "/flashcards/review/manual/$setId": typeof AuthedOnboardedFreeFlashcardsReviewManualSetIdRoute;
  "/flashcards/review/manual/all": typeof AuthedOnboardedFreeFlashcardsReviewManualAllRoute;
}

export interface FileRoutesById {
  __root__: typeof rootRoute;
  "/": typeof IndexRoute;
  "/_authed": typeof AuthedRouteRouteWithChildren;
  "/_not-authed": typeof NotAuthedRouteRouteWithChildren;
  "/recover": typeof RecoverRoute;
  "/_authed/_onboarded": typeof AuthedOnboardedRouteRouteWithChildren;
  "/_not-authed/confirm": typeof NotAuthedConfirmRoute;
  "/_not-authed/login": typeof NotAuthedLoginRoute;
  "/_not-authed/register": typeof NotAuthedRegisterRoute;
  "/_authed/_onboarded/_subscribed": typeof AuthedOnboardedSubscribedRouteRouteWithChildren;
  "/_not-authed/extension/dnd": typeof NotAuthedExtensionDndRoute;
  "/_not-authed/extension/selection": typeof NotAuthedExtensionSelectionRoute;
  "/_authed/onboarding/": typeof AuthedOnboardingIndexRoute;
  "/_authed/_onboarded/_free/_legacy": typeof AuthedOnboardedFreeLegacyRouteRouteWithChildren;
  "/_authed/_onboarded/_free/flashcards": typeof AuthedOnboardedFreeFlashcardsRouteRouteWithChildren;
  "/_authed/_onboarded/_free/search": typeof AuthedOnboardedFreeSearchRouteRouteWithChildren;
  "/_authed/_onboarded/_subscribed/_legacy": typeof AuthedOnboardedSubscribedLegacyRouteRouteWithChildren;
  "/_authed/_onboarded/_free/flashcards/review": typeof AuthedOnboardedFreeFlashcardsReviewRouteRouteWithChildren;
  "/_authed/_onboarded/_free/flashcards/trash-bin": typeof AuthedOnboardedFreeFlashcardsTrashBinRouteRouteWithChildren;
  "/_authed/_onboarded/_free/_legacy/dashboard": typeof AuthedOnboardedFreeLegacyDashboardRoute;
  "/_authed/_onboarded/_free/_legacy/profile": typeof AuthedOnboardedFreeLegacyProfileRoute;
  "/_authed/_onboarded/_free/flashcards/my-cards": typeof AuthedOnboardedFreeFlashcardsMyCardsRoute;
  "/_authed/_onboarded/_free/flashcards/my-sets": typeof AuthedOnboardedFreeFlashcardsMySetsRoute;
  "/_authed/_onboarded/_subscribed/_legacy/confirm-email-change": typeof AuthedOnboardedSubscribedLegacyConfirmEmailChangeRoute;
  "/_authed/_onboarded/_subscribed/_legacy/notifications": typeof AuthedOnboardedSubscribedLegacyNotificationsRoute;
  "/_authed/_onboarded/_subscribed/_legacy/payment-success": typeof AuthedOnboardedSubscribedLegacyPaymentSuccessRoute;
  "/_authed/_onboarded/_subscribed/_legacy/personal-space": typeof AuthedOnboardedSubscribedLegacyPersonalSpaceRoute;
  "/_authed/_onboarded/_free/flashcards/": typeof AuthedOnboardedFreeFlashcardsIndexRoute;
  "/_authed/_onboarded/_free/search/": typeof AuthedOnboardedFreeSearchIndexRoute;
  "/_authed/_onboarded/_subscribed/learning-paths/": typeof AuthedOnboardedSubscribedLearningPathsIndexRoute;
  "/_authed/_onboarded/_free/flashcards/review/$setId": typeof AuthedOnboardedFreeFlashcardsReviewSetIdRoute;
  "/_authed/_onboarded/_free/flashcards/review/all": typeof AuthedOnboardedFreeFlashcardsReviewAllRoute;
  "/_authed/_onboarded/_free/flashcards/sets/$setId": typeof AuthedOnboardedFreeFlashcardsSetsSetIdRoute;
  "/_authed/_onboarded/_free/flashcards/trash-bin/cards": typeof AuthedOnboardedFreeFlashcardsTrashBinCardsRoute;
  "/_authed/_onboarded/_free/flashcards/trash-bin/sets": typeof AuthedOnboardedFreeFlashcardsTrashBinSetsRoute;
  "/_authed/_onboarded/_subscribed/_legacy/cases/$caseId": typeof AuthedOnboardedSubscribedLegacyCasesCaseIdRoute;
  "/_authed/_onboarded/_subscribed/_legacy/dictionary/$articleId": typeof AuthedOnboardedSubscribedLegacyDictionaryArticleIdRoute;
  "/_authed/_onboarded/_subscribed/_legacy/forum/$questionId": typeof AuthedOnboardedSubscribedLegacyForumQuestionIdRoute;
  "/_authed/_onboarded/_subscribed/_legacy/learning-paths/$learningPathId": typeof AuthedOnboardedSubscribedLegacyLearningPathsLearningPathIdRoute;
  "/_authed/_onboarded/_subscribed/_legacy/admin/": typeof AuthedOnboardedSubscribedLegacyAdminIndexRoute;
  "/_authed/_onboarded/_subscribed/_legacy/forum/": typeof AuthedOnboardedSubscribedLegacyForumIndexRoute;
  "/_authed/_onboarded/_subscribed/_legacy/library/": typeof AuthedOnboardedSubscribedLegacyLibraryIndexRoute;
  "/_authed/_onboarded/_free/flashcards/review/manual/$setId": typeof AuthedOnboardedFreeFlashcardsReviewManualSetIdRoute;
  "/_authed/_onboarded/_free/flashcards/review/manual/all": typeof AuthedOnboardedFreeFlashcardsReviewManualAllRoute;
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath;
  fullPaths:
    | "/"
    | ""
    | "/recover"
    | "/confirm"
    | "/login"
    | "/register"
    | "/extension/dnd"
    | "/extension/selection"
    | "/onboarding"
    | "/flashcards"
    | "/search"
    | "/flashcards/review"
    | "/flashcards/trash-bin"
    | "/dashboard"
    | "/profile"
    | "/flashcards/my-cards"
    | "/flashcards/my-sets"
    | "/confirm-email-change"
    | "/notifications"
    | "/payment-success"
    | "/personal-space"
    | "/flashcards/"
    | "/search/"
    | "/learning-paths"
    | "/flashcards/review/$setId"
    | "/flashcards/review/all"
    | "/flashcards/sets/$setId"
    | "/flashcards/trash-bin/cards"
    | "/flashcards/trash-bin/sets"
    | "/cases/$caseId"
    | "/dictionary/$articleId"
    | "/forum/$questionId"
    | "/learning-paths/$learningPathId"
    | "/admin"
    | "/forum"
    | "/library"
    | "/flashcards/review/manual/$setId"
    | "/flashcards/review/manual/all";
  fileRoutesByTo: FileRoutesByTo;
  to:
    | "/"
    | ""
    | "/recover"
    | "/confirm"
    | "/login"
    | "/register"
    | "/extension/dnd"
    | "/extension/selection"
    | "/onboarding"
    | "/flashcards/review"
    | "/flashcards/trash-bin"
    | "/dashboard"
    | "/profile"
    | "/flashcards/my-cards"
    | "/flashcards/my-sets"
    | "/confirm-email-change"
    | "/notifications"
    | "/payment-success"
    | "/personal-space"
    | "/flashcards"
    | "/search"
    | "/learning-paths"
    | "/flashcards/review/$setId"
    | "/flashcards/review/all"
    | "/flashcards/sets/$setId"
    | "/flashcards/trash-bin/cards"
    | "/flashcards/trash-bin/sets"
    | "/cases/$caseId"
    | "/dictionary/$articleId"
    | "/forum/$questionId"
    | "/learning-paths/$learningPathId"
    | "/admin"
    | "/forum"
    | "/library"
    | "/flashcards/review/manual/$setId"
    | "/flashcards/review/manual/all";
  id:
    | "__root__"
    | "/"
    | "/_authed"
    | "/_not-authed"
    | "/recover"
    | "/_authed/_onboarded"
    | "/_not-authed/confirm"
    | "/_not-authed/login"
    | "/_not-authed/register"
    | "/_authed/_onboarded/_subscribed"
    | "/_not-authed/extension/dnd"
    | "/_not-authed/extension/selection"
    | "/_authed/onboarding/"
    | "/_authed/_onboarded/_free/_legacy"
    | "/_authed/_onboarded/_free/flashcards"
    | "/_authed/_onboarded/_free/search"
    | "/_authed/_onboarded/_subscribed/_legacy"
    | "/_authed/_onboarded/_free/flashcards/review"
    | "/_authed/_onboarded/_free/flashcards/trash-bin"
    | "/_authed/_onboarded/_free/_legacy/dashboard"
    | "/_authed/_onboarded/_free/_legacy/profile"
    | "/_authed/_onboarded/_free/flashcards/my-cards"
    | "/_authed/_onboarded/_free/flashcards/my-sets"
    | "/_authed/_onboarded/_subscribed/_legacy/confirm-email-change"
    | "/_authed/_onboarded/_subscribed/_legacy/notifications"
    | "/_authed/_onboarded/_subscribed/_legacy/payment-success"
    | "/_authed/_onboarded/_subscribed/_legacy/personal-space"
    | "/_authed/_onboarded/_free/flashcards/"
    | "/_authed/_onboarded/_free/search/"
    | "/_authed/_onboarded/_subscribed/learning-paths/"
    | "/_authed/_onboarded/_free/flashcards/review/$setId"
    | "/_authed/_onboarded/_free/flashcards/review/all"
    | "/_authed/_onboarded/_free/flashcards/sets/$setId"
    | "/_authed/_onboarded/_free/flashcards/trash-bin/cards"
    | "/_authed/_onboarded/_free/flashcards/trash-bin/sets"
    | "/_authed/_onboarded/_subscribed/_legacy/cases/$caseId"
    | "/_authed/_onboarded/_subscribed/_legacy/dictionary/$articleId"
    | "/_authed/_onboarded/_subscribed/_legacy/forum/$questionId"
    | "/_authed/_onboarded/_subscribed/_legacy/learning-paths/$learningPathId"
    | "/_authed/_onboarded/_subscribed/_legacy/admin/"
    | "/_authed/_onboarded/_subscribed/_legacy/forum/"
    | "/_authed/_onboarded/_subscribed/_legacy/library/"
    | "/_authed/_onboarded/_free/flashcards/review/manual/$setId"
    | "/_authed/_onboarded/_free/flashcards/review/manual/all";
  fileRoutesById: FileRoutesById;
}

export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute;
  AuthedRouteRoute: typeof AuthedRouteRouteWithChildren;
  NotAuthedRouteRoute: typeof NotAuthedRouteRouteWithChildren;
  RecoverRoute: typeof RecoverRoute;
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  AuthedRouteRoute: AuthedRouteRouteWithChildren,
  NotAuthedRouteRoute: NotAuthedRouteRouteWithChildren,
  RecoverRoute: RecoverRoute,
};

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>();

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.tsx",
      "children": [
        "/",
        "/_authed",
        "/_not-authed",
        "/recover"
      ]
    },
    "/": {
      "filePath": "index.tsx"
    },
    "/_authed": {
      "filePath": "_authed/route.tsx",
      "children": [
        "/_authed/_onboarded",
        "/_authed/onboarding/"
      ]
    },
    "/_not-authed": {
      "filePath": "_not-authed/route.tsx",
      "children": [
        "/_not-authed/confirm",
        "/_not-authed/login",
        "/_not-authed/register",
        "/_not-authed/extension/dnd",
        "/_not-authed/extension/selection"
      ]
    },
    "/recover": {
      "filePath": "recover.tsx"
    },
    "/_authed/_onboarded": {
      "filePath": "_authed/_onboarded/route.tsx",
      "parent": "/_authed",
      "children": [
        "/_authed/_onboarded/_subscribed",
        "/_authed/_onboarded/_free/_legacy",
        "/_authed/_onboarded/_free/flashcards",
        "/_authed/_onboarded/_free/search"
      ]
    },
    "/_not-authed/confirm": {
      "filePath": "_not-authed/confirm.tsx",
      "parent": "/_not-authed"
    },
    "/_not-authed/login": {
      "filePath": "_not-authed/login.tsx",
      "parent": "/_not-authed"
    },
    "/_not-authed/register": {
      "filePath": "_not-authed/register.tsx",
      "parent": "/_not-authed"
    },
    "/_authed/_onboarded/_subscribed": {
      "filePath": "_authed/_onboarded/_subscribed/route.tsx",
      "parent": "/_authed/_onboarded",
      "children": [
        "/_authed/_onboarded/_subscribed/_legacy",
        "/_authed/_onboarded/_subscribed/learning-paths/"
      ]
    },
    "/_not-authed/extension/dnd": {
      "filePath": "_not-authed/extension/dnd.tsx",
      "parent": "/_not-authed"
    },
    "/_not-authed/extension/selection": {
      "filePath": "_not-authed/extension/selection.tsx",
      "parent": "/_not-authed"
    },
    "/_authed/onboarding/": {
      "filePath": "_authed/onboarding/index.tsx",
      "parent": "/_authed"
    },
    "/_authed/_onboarded/_free/_legacy": {
      "filePath": "_authed/_onboarded/_free/_legacy/route.tsx",
      "parent": "/_authed/_onboarded",
      "children": [
        "/_authed/_onboarded/_free/_legacy/dashboard",
        "/_authed/_onboarded/_free/_legacy/profile"
      ]
    },
    "/_authed/_onboarded/_free/flashcards": {
      "filePath": "_authed/_onboarded/_free/flashcards/route.tsx",
      "parent": "/_authed/_onboarded",
      "children": [
        "/_authed/_onboarded/_free/flashcards/review",
        "/_authed/_onboarded/_free/flashcards/trash-bin",
        "/_authed/_onboarded/_free/flashcards/my-cards",
        "/_authed/_onboarded/_free/flashcards/my-sets",
        "/_authed/_onboarded/_free/flashcards/",
        "/_authed/_onboarded/_free/flashcards/sets/$setId"
      ]
    },
    "/_authed/_onboarded/_free/search": {
      "filePath": "_authed/_onboarded/_free/search/route.tsx",
      "parent": "/_authed/_onboarded",
      "children": [
        "/_authed/_onboarded/_free/search/"
      ]
    },
    "/_authed/_onboarded/_subscribed/_legacy": {
      "filePath": "_authed/_onboarded/_subscribed/_legacy/route.tsx",
      "parent": "/_authed/_onboarded/_subscribed",
      "children": [
        "/_authed/_onboarded/_subscribed/_legacy/confirm-email-change",
        "/_authed/_onboarded/_subscribed/_legacy/notifications",
        "/_authed/_onboarded/_subscribed/_legacy/payment-success",
        "/_authed/_onboarded/_subscribed/_legacy/personal-space",
        "/_authed/_onboarded/_subscribed/_legacy/cases/$caseId",
        "/_authed/_onboarded/_subscribed/_legacy/dictionary/$articleId",
        "/_authed/_onboarded/_subscribed/_legacy/forum/$questionId",
        "/_authed/_onboarded/_subscribed/_legacy/learning-paths/$learningPathId",
        "/_authed/_onboarded/_subscribed/_legacy/admin/",
        "/_authed/_onboarded/_subscribed/_legacy/forum/",
        "/_authed/_onboarded/_subscribed/_legacy/library/"
      ]
    },
    "/_authed/_onboarded/_free/flashcards/review": {
      "filePath": "_authed/_onboarded/_free/flashcards/review/route.tsx",
      "parent": "/_authed/_onboarded/_free/flashcards",
      "children": [
        "/_authed/_onboarded/_free/flashcards/review/$setId",
        "/_authed/_onboarded/_free/flashcards/review/all",
        "/_authed/_onboarded/_free/flashcards/review/manual/$setId",
        "/_authed/_onboarded/_free/flashcards/review/manual/all"
      ]
    },
    "/_authed/_onboarded/_free/flashcards/trash-bin": {
      "filePath": "_authed/_onboarded/_free/flashcards/trash-bin/route.tsx",
      "parent": "/_authed/_onboarded/_free/flashcards",
      "children": [
        "/_authed/_onboarded/_free/flashcards/trash-bin/cards",
        "/_authed/_onboarded/_free/flashcards/trash-bin/sets"
      ]
    },
    "/_authed/_onboarded/_free/_legacy/dashboard": {
      "filePath": "_authed/_onboarded/_free/_legacy/dashboard.tsx",
      "parent": "/_authed/_onboarded/_free/_legacy"
    },
    "/_authed/_onboarded/_free/_legacy/profile": {
      "filePath": "_authed/_onboarded/_free/_legacy/profile.tsx",
      "parent": "/_authed/_onboarded/_free/_legacy"
    },
    "/_authed/_onboarded/_free/flashcards/my-cards": {
      "filePath": "_authed/_onboarded/_free/flashcards/my-cards.tsx",
      "parent": "/_authed/_onboarded/_free/flashcards"
    },
    "/_authed/_onboarded/_free/flashcards/my-sets": {
      "filePath": "_authed/_onboarded/_free/flashcards/my-sets.tsx",
      "parent": "/_authed/_onboarded/_free/flashcards"
    },
    "/_authed/_onboarded/_subscribed/_legacy/confirm-email-change": {
      "filePath": "_authed/_onboarded/_subscribed/_legacy/confirm-email-change.tsx",
      "parent": "/_authed/_onboarded/_subscribed/_legacy"
    },
    "/_authed/_onboarded/_subscribed/_legacy/notifications": {
      "filePath": "_authed/_onboarded/_subscribed/_legacy/notifications.tsx",
      "parent": "/_authed/_onboarded/_subscribed/_legacy"
    },
    "/_authed/_onboarded/_subscribed/_legacy/payment-success": {
      "filePath": "_authed/_onboarded/_subscribed/_legacy/payment-success.tsx",
      "parent": "/_authed/_onboarded/_subscribed/_legacy"
    },
    "/_authed/_onboarded/_subscribed/_legacy/personal-space": {
      "filePath": "_authed/_onboarded/_subscribed/_legacy/personal-space.tsx",
      "parent": "/_authed/_onboarded/_subscribed/_legacy"
    },
    "/_authed/_onboarded/_free/flashcards/": {
      "filePath": "_authed/_onboarded/_free/flashcards/index.tsx",
      "parent": "/_authed/_onboarded/_free/flashcards"
    },
    "/_authed/_onboarded/_free/search/": {
      "filePath": "_authed/_onboarded/_free/search/index.tsx",
      "parent": "/_authed/_onboarded/_free/search"
    },
    "/_authed/_onboarded/_subscribed/learning-paths/": {
      "filePath": "_authed/_onboarded/_subscribed/learning-paths/index.tsx",
      "parent": "/_authed/_onboarded/_subscribed"
    },
    "/_authed/_onboarded/_free/flashcards/review/$setId": {
      "filePath": "_authed/_onboarded/_free/flashcards/review/$setId.tsx",
      "parent": "/_authed/_onboarded/_free/flashcards/review"
    },
    "/_authed/_onboarded/_free/flashcards/review/all": {
      "filePath": "_authed/_onboarded/_free/flashcards/review/all.tsx",
      "parent": "/_authed/_onboarded/_free/flashcards/review"
    },
    "/_authed/_onboarded/_free/flashcards/sets/$setId": {
      "filePath": "_authed/_onboarded/_free/flashcards/sets/$setId.tsx",
      "parent": "/_authed/_onboarded/_free/flashcards"
    },
    "/_authed/_onboarded/_free/flashcards/trash-bin/cards": {
      "filePath": "_authed/_onboarded/_free/flashcards/trash-bin/cards.tsx",
      "parent": "/_authed/_onboarded/_free/flashcards/trash-bin"
    },
    "/_authed/_onboarded/_free/flashcards/trash-bin/sets": {
      "filePath": "_authed/_onboarded/_free/flashcards/trash-bin/sets.tsx",
      "parent": "/_authed/_onboarded/_free/flashcards/trash-bin"
    },
    "/_authed/_onboarded/_subscribed/_legacy/cases/$caseId": {
      "filePath": "_authed/_onboarded/_subscribed/_legacy/cases/$caseId.tsx",
      "parent": "/_authed/_onboarded/_subscribed/_legacy"
    },
    "/_authed/_onboarded/_subscribed/_legacy/dictionary/$articleId": {
      "filePath": "_authed/_onboarded/_subscribed/_legacy/dictionary/$articleId.tsx",
      "parent": "/_authed/_onboarded/_subscribed/_legacy"
    },
    "/_authed/_onboarded/_subscribed/_legacy/forum/$questionId": {
      "filePath": "_authed/_onboarded/_subscribed/_legacy/forum/$questionId.tsx",
      "parent": "/_authed/_onboarded/_subscribed/_legacy"
    },
    "/_authed/_onboarded/_subscribed/_legacy/learning-paths/$learningPathId": {
      "filePath": "_authed/_onboarded/_subscribed/_legacy/learning-paths/$learningPathId.tsx",
      "parent": "/_authed/_onboarded/_subscribed/_legacy"
    },
    "/_authed/_onboarded/_subscribed/_legacy/admin/": {
      "filePath": "_authed/_onboarded/_subscribed/_legacy/admin/index.tsx",
      "parent": "/_authed/_onboarded/_subscribed/_legacy"
    },
    "/_authed/_onboarded/_subscribed/_legacy/forum/": {
      "filePath": "_authed/_onboarded/_subscribed/_legacy/forum/index.tsx",
      "parent": "/_authed/_onboarded/_subscribed/_legacy"
    },
    "/_authed/_onboarded/_subscribed/_legacy/library/": {
      "filePath": "_authed/_onboarded/_subscribed/_legacy/library/index.tsx",
      "parent": "/_authed/_onboarded/_subscribed/_legacy"
    },
    "/_authed/_onboarded/_free/flashcards/review/manual/$setId": {
      "filePath": "_authed/_onboarded/_free/flashcards/review/manual/$setId.tsx",
      "parent": "/_authed/_onboarded/_free/flashcards/review"
    },
    "/_authed/_onboarded/_free/flashcards/review/manual/all": {
      "filePath": "_authed/_onboarded/_free/flashcards/review/manual/all.tsx",
      "parent": "/_authed/_onboarded/_free/flashcards/review"
    }
  }
}
ROUTE_MANIFEST_END */

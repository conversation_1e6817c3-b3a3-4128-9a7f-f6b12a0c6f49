// import { Layout } from "~/components/layouts/Layout.tsx";
// import PageHead from "~/components/organisms/pageHead/PageHead.tsx";
import { AdminPage } from "~/components/pages/AdminPage/AdminPage.tsx";
import { usePageTitleUpdate } from "~/hooks/usePageTitleUpdate.ts";
import { api } from "~/utils/trpc.ts";

import { createFileRoute, redirect } from "@tanstack/react-router";
import { type FunctionComponent } from "react";

const AdminPageComponent: FunctionComponent = () =>
{
  usePageTitleUpdate("Admin");

  return (
    <>
      {/* <PageHead pageTitle={"Admin"}/> */}
      <AdminPage />
    </>
  );
};

export const Route = createFileRoute("/_authed/_onboarded/_subscribed/_legacy/admin/")({
  component: AdminPageComponent,
  loader: async ({ context: { queryClient } }) =>
  {
    const user = await queryClient.ensureQueryData(api.users.getUserDetails.queryOptions());
    if (!user?.roles?.some((r) => r.identifier === "admin"))
    {
      throw redirect({ to: "/dashboard" });
    }
  },
});

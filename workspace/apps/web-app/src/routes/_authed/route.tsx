import { ActivityWatchdog } from "~/components/helpers/activityWatchdog/ActivityWatchdog.ts";
import { SystemMessages } from "~/components/helpers/systemMessgaes/SystemMessages.tsx";
import { Sidebar } from "~/components/layout/sidebar/Sidebar.tsx";
import { FeedbackButtonWrapper } from "~/components/molecules/feedbackButton/FeedbackButton.tsx";
import Lightbox from "~/components/molecules/lightbox/Lightbox.tsx";
import NewNotificationEarnedWatchdog from "~/components/molecules/newNotificationEarnedWatchdog/NewNotificationEarnedWatchdog.tsx";
import { ScrollToTopButton } from "~/components/molecules/scrollToTopButton/ScrollToTopButton.tsx";
import FileViewer from "~/components/organisms/fileViewer/FileViewer.tsx";
import { MaterialTagsDrawer } from "~/components/organisms/materialTagsDrawer/MaterialTagsDrawer.tsx";
import DocumentEditor from "~/components/organisms/papersBlock/documentEditor/DocumentEditor.tsx";
import { SidebarProvider } from "~/components/ui/sidebar.tsx";
import useContextAndErrorIfNull from "~/hooks/useContextAndErrorIfNull.ts";
import { useSignout } from "~/hooks/useSignout.ts";
import { supabase } from "~/lib/supabase.ts";
import { InvalidateQueriesContext } from "~/providers/InvalidateQueriesProvider.tsx";
import MeilisearchProvider from "~/providers/MeilisearchProvider.tsx";
import { UserProvider } from "~/providers/UserProvider.tsx";
import { useSidebarStore } from "~/stores/sidebar.store.ts";
import { type GtmUserData, sendGTMEvent } from "~/utils/gtm.ts";
import { api } from "~/utils/trpc.ts";

import { queryParams } from "@constellatio/shared/query-params";
import { createFileRoute, Outlet, redirect } from "@tanstack/react-router";
import { zodValidator } from "@tanstack/zod-adapter";
import { useEffect } from "react";
import { z } from "zod";

const authedRouteSearchSchema = z.object({
  [queryParams.socialSignUpSuccess]: z.boolean().optional(),
  [queryParams.socialLoginSuccess]: z.boolean().optional(),
  [queryParams.socialProvider]: z.string().optional(),
});

export const Route = createFileRoute("/_authed")({
  validateSearch: zodValidator(authedRouteSearchSchema),
  component: AuthedRoute,
  beforeLoad: async ({ context, location, search }) =>
  {
    if (!context.auth?.isAuthenticated)
    {
      throw redirect({
        search: {
          [queryParams.redirectedFrom]: location.href,
        },
        to: "/login",
      });
    }

    const user = await context.queryClient.ensureQueryData(api.users.getUserDetails.queryOptions());

    const gtmUserData: GtmUserData = {
      email: user.email,
      first_name: user.firstName ?? undefined,
      last_name: user.lastName ?? undefined,
      user_id: user.id,
    };

    if (search[queryParams.socialSignUpSuccess])
    {
      sendGTMEvent(
        {
          name: "sign_up",
          params: {
            method: search[queryParams.socialProvider],
          },
        },
        gtmUserData
      );
    }
    else if (search[queryParams.socialLoginSuccess])
    {
      sendGTMEvent(
        {
          name: "login",
          params: {
            method: search[queryParams.socialProvider],
          },
        },
        gtmUserData
      );
    }
  },
  loader: ({ context }) => ({ user: context.auth?.user }),
});

function AuthedRoute()
{
  const { handleSignOut } = useSignout();
  const { user } = Route.useLoaderData();
  const isSidebarOpen = useSidebarStore((s) => s.isSidebarOpen);
  // const { subscribeToAuthStateChanges } = useContextAndErrorIfNull(AuthContext);

  const { invalidateSubscriptionDetails } = useContextAndErrorIfNull(InvalidateQueriesContext);

  if (!user)
  {
    throw new Error("User not found in authed route");
  }

  useEffect(() =>
  {
    const onUnauthorizedResponse = (event: CustomEvent<ReceivedUnauthorizedResponseEventDetail>) =>
    {
      console.log("Unauthorized response event received", event.detail);
      void handleSignOut();
    };

    document.addEventListener("received-unauthorized-response", onUnauthorizedResponse);
    return () => document.removeEventListener("received-unauthorized-response", onUnauthorizedResponse);
  }, [handleSignOut]);

  useEffect(() =>
  {
    const channel = supabase
      .channel("realtime subscription status")
      .on("postgres_changes", { event: "UPDATE", schema: "public", table: "UserOrder" }, (_payload) =>
      {
        void invalidateSubscriptionDetails();
      })
      .on("postgres_changes", { event: "INSERT", schema: "public", table: "UserOrder" }, (_payload) =>
      {
        void invalidateSubscriptionDetails();
      })
      .on("postgres_changes", { event: "DELETE", schema: "public", table: "UserOrder" }, (_payload) =>
      {
        void invalidateSubscriptionDetails();
      })
      .subscribe();

    return () => void supabase.removeChannel(channel);
  }, [invalidateSubscriptionDetails]);

  return (
    <UserProvider user={user}>
      <MeilisearchProvider>
        <FeedbackButtonWrapper />
        <NewNotificationEarnedWatchdog />
        <Lightbox />
        <ScrollToTopButton />
        <FileViewer />
        <DocumentEditor />
        <MaterialTagsDrawer />
        <ActivityWatchdog />
        <SystemMessages />
        <SidebarProvider open={isSidebarOpen}>
          <Sidebar />
          <Outlet />
        </SidebarProvider>
      </MeilisearchProvider>
    </UserProvider>
  );
}

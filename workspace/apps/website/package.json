{"name": "@constellatio/website", "version": "0.1.0", "private": true, "type": "module", "prettier": "@constellatio/prettier-config", "scripts": {"build": "next build", "clean": "git clean -xdf .cache .next .turbo dist node_modules", "clear": "git clean -xdf .cache .turbo .next dist", "dev": "pnpm with-env next dev -p 3030", "start": "pnpm with-env next start -p 3030", "typecheck": "tsc --noEmit", "typegen": "tsc --noEmit", "ui-add": "pnpm dlx shadcn@latest add", "with-env": "dotenv-run -- ", "prettierSeperator": "--------------------------------------- FORMATTING ---------------------------------------", "format": "prettier --check . ", "format:fix": "prettier --write . ", "eslintSeperator": "--------------------------------------- LINTING ------------------------------------------", "lint": "eslint src", "lint:fix": "eslint --fix src"}, "dependencies": {"@caisy/rich-text-react-renderer": "catalog:", "@constellatio/cms": "workspace:*", "@constellatio/env": "workspace:*", "@constellatio/shared": "workspace:*", "@constellatio/shared-ui": "workspace:*", "@constellatio/utility-types": "workspace:*", "@hookform/resolvers": "catalog:", "@mantine/hooks": "6.0.21", "@next/third-parties": "catalog:", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "2.1.0", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-scroll-area": "1.2.3", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slot": "1.1.0", "@radix-ui/react-tabs": "1.1.2", "@tailwindcss/container-queries": "0.1.1", "@tailwindcss/typography": "0.5.16", "@vercel/blob": "0.27.0", "axios": "catalog:", "class-variance-authority": "0.7.1", "clsx": "2.1.1", "embla-carousel-react": "8.5.2", "lucide-react": "0.468.0", "next": "catalog:", "plaiceholder": "3.0.0", "posthog-js": "catalog:", "react": "catalog:", "react-blurhash": "0.3.0", "react-dom": "catalog:", "react-hook-form": "catalog:", "sharp": "0.33.5", "tailwind-merge": "2.5.5", "tailwindcss": "catalog:", "tailwindcss-animate": "1.0.7", "zod": "catalog:", "zustand": "catalog:"}, "devDependencies": {"@constellatio/eslint-config": "workspace:*", "@constellatio/prettier-config": "workspace:*", "@constellatio/tsconfig": "workspace:*", "@constellatio/utils": "workspace:*", "@total-typescript/ts-reset": "catalog:", "@types/node": "catalog:", "@types/react": "catalog:", "@types/react-dom": "catalog:", "eslint": "catalog:", "eslint-config-next": "14.2.29", "jiti": "catalog:", "postcss": "catalog:", "prettier": "catalog:", "prettier-plugin-merge": "0.7.2", "prettier-plugin-tailwindcss": "0.6.11", "typescript": "catalog:"}}
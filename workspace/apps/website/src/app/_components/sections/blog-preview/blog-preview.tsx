import { BlogArticle } from "@/app/_components/blog/overview-page/components/blog-article";
import { Heading } from "@/app/_components/common/heading";
import { ContentWrapper } from "@/app/_components/layout/content-wrapper";
import { Button } from "@/components/ui/button";
import { paths } from "@/utils/paths";

import { getNewestBlogArticles } from "@constellatio/cms/website/content/getNewestBlogArticles";
import { ArrowRight } from "lucide-react";
import Link from "next/link";
import { type FunctionComponent } from "react";

export const BlogPreview: FunctionComponent = async () =>
{
  const newestBlogArticles = await getNewestBlogArticles(3);

  return (
    <ContentWrapper className={"flex flex-col items-center gap-12"}>
      <Heading level={2} title={"Mehr als Jura"} badge={{ children: "Blog", variant: "red" }} />
      <div className={"flex w-full flex-wrap gap-5 max-md:flex-col max-md:flex-nowrap"}>
        {newestBlogArticles.map((article) => (
          <div key={article.id} className={"min-w-[280px] grow @container"}>
            <BlogArticle articleType={"blog"} article={article} />
          </div>
        ))}
      </div>
      <Link href={paths.blog} className={"self-center text-center"}>
        <Button variant={"outline"}>
          Mehr lesen <ArrowRight size={20} />
        </Button>
      </Link>
    </ContentWrapper>
  );
};

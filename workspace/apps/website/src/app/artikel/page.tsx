import { Heading } from "@/app/_components/common/heading.tsx";
import { ContentWrapper } from "@/app/_components/layout/content-wrapper.tsx";
import { paths } from "@/utils/paths.ts";

import { getAllArticlesPublishedForWebsiteFromCaisy } from "@constellatio/cms/app/content/getAllArticlesPublishedForWebsite";
import { type Metadata } from "next";
import Link from "next/link";
import { Fragment } from "react";

export const dynamicParams = true;
export const revalidate = 0;

export const metadata: Metadata = {
  description: "Übersicht der Artikel von Constellatio",
  title: "Artikel",
};

const Page = async () =>
{
  const articlesPublishedForWebsite = await getAllArticlesPublishedForWebsiteFromCaisy();

  return (
    <Fragment>
      <div className={"pb-24 rounded-b-3xl bg-accent text-muted-0 max-md:rounded-b-lg"}>
        <ContentWrapper className={"relative text-center"}>
          <Heading
            level={1}
            badge={{
              children: "Inhalte",
              variant: "white",
            }}
            title={"Constellatio Artikel"}
          />
        </ContentWrapper>
      </div>
      <ContentWrapper className={"mt-20"} variant={"small"}>
        <div className={"flex flex-col"}>
          {articlesPublishedForWebsite
            .sort((a, b) => (a.title! > b.title! ? 1 : -1))
            .map((article) => (
              <Link
                href={`${paths.artikel}/${article?.slug}`}
                key={article?.id}
                prefetch={false}
                className={
                  "flex gap-5 items-center justify-between border-b border-muted-5 py-1 hover:text-muted-8 hover:underline last-of-type:border-b-0"
                }>
                <strong className={"text-lg font-semibold max-md:text-base max-md:leading-tight"}>
                  {article?.title}
                </strong>
                <span className={"text-muted-7 font-medium min-w-max"}>{article?.legalArea?.legalAreaName}</span>
              </Link>
            ))}
        </div>
      </ContentWrapper>
    </Fragment>
  );
};

export default Page;

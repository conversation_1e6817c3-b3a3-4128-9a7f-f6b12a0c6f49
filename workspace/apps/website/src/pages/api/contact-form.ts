import { env } from "@constellatio/env";
import axios, { AxiosError } from "axios";
import { type <PERSON><PERSON>pi<PERSON><PERSON><PERSON> } from "next";
import { z } from "zod";

export const contactFormRequestSchema = z.object({
  email: z.string().email(),
  message: z.string().min(1),
  name: z.string().min(5),
});

export type ContactFormRequest = z.infer<typeof contactFormRequestSchema>;

const handler: NextApiHandler = async (req, res) =>
{
  if (req.method !== "POST")
  {
    return res.status(500).json({ error: "Wrong request method!" });
  }

  const parseBodyResult = contactFormRequestSchema.safeParse(req.body);

  if (!parseBodyResult.success)
  {
    console.error("Invalid body", parseBodyResult.error);
    return res.status(400).json({ error: parseBodyResult.error });
  }

  const parsedBody = parseBodyResult.data;

  try
  {
    await axios.post(
      "https://app.loops.so/api/v1/transactional",
      {
        dataVariables: parsedBody,
        email: "<EMAIL>",
        transactionalId: "cm7b4qyj305as12chz0r3ek7s",
      },
      {
        headers: {
          Authorization: `Bearer ${env.SHARED_LOOPS_API_KEY}`,
          "Content-Type": "application/json",
        },
      }
    );
  }
  catch (error)
  {
    if (error instanceof AxiosError)
    {
      console.error("Sending transactional email failed:", {
        data: error.response?.data,
        status: error.response?.status,
        statusText: error.response?.statusText,
      });
    }
    else
    {
      console.error("Sending transactional email failed:", error);
    }

    return res.status(500).json({ error: "Failed to send message" });
  }

  return res.status(200).json({ success: true });
};

export default handler;

/* eslint-disable import/no-unused-modules */
import { type CodegenConfig } from "@graphql-codegen/cli";

let project: "app" | "website";
let projectId: string;

if (process.env.PROJECT === "app")
{
  project = "app";
  projectId = process.env.WEBAPP_CAISY_PROJECT_ID!;
}
else if (process.env.PROJECT === "website")
{
  project = "website";
  projectId = process.env.WEBSITE_CAISY_PROJECT_ID!;
}
else
{
  throw new Error("Invalid project. Project must be either 'app' or 'website'");
}

if (!projectId || projectId === "")
{
  throw new Error(`Invalid projectId for project '${project}'. Please add it to the env file`);
}

export const config: CodegenConfig = {
  config: {
    gqlImport: "graphql-tag#gql", // import { gql } from 'graphql-tag' instead of import gql from 'graphql-tag'
  },
  emitLegacyCommonJSImports: false,
  generates: {
    [`src/${project}/graphql/__generated/graphql.schema.graphql`]: {
      plugins: ["schema-ast"],
    },
    [`src/${project}/graphql/__generated/graphql.schema.json`]: {
      plugins: ["introspection"],
    },
    [`src/${project}/graphql/__generated/sdk.ts`]: {
      config: {
        dedupeFragments: true,
        exportFragmentSpreadSubTypes: true,
        inlineFragmentTypes: "combine",
        preResolveTypes: true,
        rawRequest: false,
        skipTypename: false,
        typesPrefix: "IGen",
      },
      documents: [
        `src/${project}/graphql/**/*.graphql`,
        `src/${project}/graphql/fragments/**/*.ts`,
        `src/${project}/graphql/queries/**/*.ts`,
      ],
      plugins: ["typescript", "typescript-operations", "typescript-generic-sdk"],
    },
  },
  ignoreNoDocuments: true,
  noSilentErrors: true,
  overwrite: true,
  schema: [
    {
      [`https://cloud.caisy.io/api/v3/e/${projectId}/graphql`]: {
        headers: {
          "x-caisy-apikey": `${process.env.SHARED_CAISY_API_KEY}`,
        },
      },
    },
  ],
};

export default config;

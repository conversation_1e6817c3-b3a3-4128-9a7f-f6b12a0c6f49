import { gql } from "graphql-request";

import { f_SystemMessage } from "../fragments/SystemMessage.js";

export const q_allSystemMessages = gql`
  ${f_SystemMessage}
  query getAllSystemMessages($after: String) {
    allSystemMessage(first: 100, after: $after) {
      totalCount
      pageInfo {
        endCursor
        hasNextPage
      }
      edges {
        node {
          ...SystemMessage
        }
      }
    }
  }
`;

/* eslint-disable @typescript-eslint/quotes */
import { env } from "@constellatio/env";

import { type DbConnection } from "../../drizzle.js";
import { buildConflictUpdateColumns } from "../../index.js";
import { constellatioUtils_environmentVariables, type EnvironmentVariableInsert } from "../../schema.js";

export const allEnvironmentVariables = [
  {
    description: "Webhook URL to notify our application backend about data changes",
    key: "webhook_url",
    type: "string",
    value: "http://host.docker.internal:3010/api/integration/webhooks/supabase",
  },
  {
    description: "Headers to send with the webhook request",
    key: "webhook_headers",
    type: "object",
    value: `{"content-type": "application/json", "authorization": "Bearer ${env.WEBAPP_SUPABASE_WEBHOOK_SECRET}"}`,
  },
] as const satisfies EnvironmentVariableInsert[];

export const seedEnvironmentVariables = async (dbConnection: DbConnection) =>
{
  await dbConnection
    .insert(constellatioUtils_environmentVariables)
    .values(allEnvironmentVariables)
    .onConflictDoUpdate({
      set: buildConflictUpdateColumns(constellatioUtils_environmentVariables, ["value", "description", "type"]),
      target: [constellatioUtils_environmentVariables.key],
    });
};

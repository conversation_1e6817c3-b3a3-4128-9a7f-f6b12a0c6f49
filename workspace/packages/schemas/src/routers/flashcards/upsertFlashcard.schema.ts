import { z } from "zod";

import { problemFlashcardContentSchema } from "./flashcard-types/problem.schema.js";
import { schemaFlashcardContentSchema } from "./flashcard-types/schema.schema.js";
import {
  standardFlashcardContentSchema,
  clozeDeletionFlashcardContentSchema,
} from "../../common/flashcards/flashcard.validation.js";

export const flashcardContentSchema = z.discriminatedUnion("type", [
  standardFlashcardContentSchema,
  clozeDeletionFlashcardContentSchema,
  problemFlashcardContentSchema,
  schemaFlashcardContentSchema,
]);

// Wir definieren das Schema als Union der beiden Typen
export const upsertFlashcardSchema = z.object({
  clozeDeletionCardId: z.string().optional(),
  clozeDeletionIndex: z.number().optional(),
  content: flashcardContentSchema,
  id: z.string().optional(),
  setIds: z.array(z.string()).min(1, { message: "Mindestens ein Set muss ausgewählt werden" }),
  tagIds: z.array(z.string()).optional(),
});

export type UpsertFlashcardSchema = z.infer<typeof upsertFlashcardSchema>;
